import type { 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>b<PERSON>ate<PERSON>y,
  DbQuiz,
  DbQuestion,
  DbQuestionOption,
  DbQuestionType,
  DbQuizType,
  DbExplanation,
  DbQuizAttempt,
  DbUserAnswer,
  DbUserProgress,
  DbBadge,
  DbUserBadge
} from 'wf-shared/types';

// User and Authentication Types
// Extends database type with composed properties
export interface User extends DbUser {
  // Populated via joins
  level?: Level;
  
  // Auth-related properties (not in database)
  email_confirmed_at?: string;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<{ user: User | null; error?: string }>;
  signUp: (email: string, password: string, userData: { first_name: string; last_name: string; level_id: string }) => Promise<{ user: User | null; error?: string }>;
  signOut: () => Promise<void>;
}

// Level System Types - matches database exactly
export type Level = DbLevel;

// Category Types - extends database type with composed properties
export interface Category extends DbCategory {
  // Composed properties for hierarchical display
  parent?: Category; // For nested categories
  children?: Category[]; // For hierarchical display
}

// Quiz and Question Types
export type QuizMode = 'practice' | 'test';

// Quiz and Question Type interfaces - match database exactly
export type QuizType = DbQuizType;

export type QuestionType = DbQuestionType;

// Quiz interface - extends database type with composed properties
export interface Quiz extends DbQuiz {
  // Populated via joins
  category?: Category;
  level?: Level;
  quiz_type?: QuizType;
  // Optional questions array for optimization (when fetched with quiz)
  questions?: QuizQuestion[];
}

// Type for raw Supabase response with nested joins
export interface SupabaseQuizResponse {
  id: string;
  key: string;
  title: string | null;
  description?: string | null;
  instructions?: string | null;
  category_id: string;
  level_id: string;
  quiz_type_id?: string | null;
  difficulty_level: number;
  total_questions: number;
  time_limit_minutes?: number | null;
  quiz_config?: Record<string, unknown>;
  is_active: boolean | null;
  created_at: string | null;
  updated_at: string | null;
  
  // Nested relations from Supabase joins
  categories?: {
    id: string;
    key: string;
    name: string | null;
    description?: string | null;
    category_type: string | null;
  };
  levels?: {
    id: string;
    system: string;
    key: string;
    name: string;
    description?: string | null;
    sort_order: number | null;
  };
  quiz_types?: {
    id: string;
    key: string;
    name: string;
    description?: string;
  } | null;
}

// Question-related interfaces
export type QuestionOption = DbQuestionOption;

export type Explanation = DbExplanation;

// QuizQuestion interface - extends database type with composed properties
export interface QuizQuestion extends DbQuestion {
  // Populated via joins
  question_type?: QuestionType;
  options?: QuestionOption[];
  explanations?: Explanation[];
  
  // Derived properties for easier access
  correct_answer?: string | null; // The correct answer text
}

// Quiz attempt and user answer interfaces - match database exactly
export type QuizAttempt = DbQuizAttempt;

export type UserAnswer = DbUserAnswer;

export interface QuizResults {
  correct: number;
  total: number;
  answered: number;
  percentage: number;
  timeSpent: number;
  passed: boolean;
  mode: QuizMode;
  answers: QuizAnswer[];
}

export interface QuizAnswer {
  questionId: string;
  userAnswer: string;
  correctAnswer: string;
  isCorrect: boolean;
  timeSpent?: number;
}

// Progress and Badge Types - extend database types with composed properties
export interface UserProgress extends DbUserProgress {
  // Populated via joins
  level?: Level;
  category?: Category;
}

export type Badge = DbBadge;

export interface UserBadge extends DbUserBadge {
  // Populated via join
  badge?: Badge;
}

// Component Props Types
export interface QuizModeSelectionProps {
  quiz?: Quiz;
  onModeSelect?: (mode: QuizMode) => void;
  onBack?: () => void;
}

export interface QuizResultsProps {
  results: QuizResults;
  quiz: Quiz;
  mode: QuizMode;
  userAnswers: Record<string, string>;
  questions: QuizQuestion[];
}

export interface QuizQuestionProps {
  question: QuizQuestion;
  selectedAnswer?: string;
  onAnswerSelect: (answer: string) => void;
  showFeedback?: boolean;
  mode: QuizMode;
  questionNumber: number;
  totalQuestions: number;
}

export interface ProtectedRouteProps {
  children: React.ReactNode;
}

// Layout Component Props
export interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

// Progress and Statistics Types
export interface UserStats {
  totalAttempts: number;
  averageScore: number;
  bestScore: number;
  totalTimeSpent: number;
}

export interface PerformanceLevel {
  level: string;
  color: string;
  bg: string;
}

export interface ScoreBadge {
  text: string;
  class: string;
}

// API Response Types
export interface SupabaseResponse<T> {
  data: T | null;
  error: Error | null;
}

export interface AuthResponse {
  user: User | null;
  error?: string;
}

// Form Types
export interface LoginFormData {
  email: string;
  password: string;
}

export interface SignUpFormData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  level_id: string;
}

// Navigation and Routing Types
export interface RouteParams {
  quizId?: string;
}

export interface LocationState {
  results?: QuizResults;
  quiz?: Quiz;
  mode?: QuizMode;
  questions?: QuizQuestion[];
}

// Common Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface ErrorState {
  message: string;
  code?: string;
}

// Updated Database Tables interface
export interface DatabaseTables {
  users: User;
  levels: Level;
  categories: Category;
  quizzes: Quiz;
  questions: QuizQuestion;
  question_options: QuestionOption;
  question_types: QuestionType;
  quiz_types: QuizType;
  explanations: Explanation;
  quiz_attempts: QuizAttempt;
  user_answers: UserAnswer;
  user_progress: UserProgress;
  badges: Badge;
  user_badges: UserBadge;
}

// Event Handler Types
export type ButtonClickHandler = (event: React.MouseEvent<HTMLButtonElement>) => void;
export type FormSubmitHandler = (event: React.FormEvent<HTMLFormElement>) => void;
export type InputChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => void;
export type SelectChangeHandler = (event: React.ChangeEvent<HTMLSelectElement>) => void;

// Vocabulary Builder Types
export interface AwlWord {
  id: string;
  word: string;
  sublists: number; // Note: database uses 'sublists' not 'sublist'
  frequency_rank: number;
  word_forms?: Record<string, string> | string; // Can be JSON object or string
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

export interface WordDefinition {
  id: string;
  awl_word_id: string;
  word_form: string;
  word_text: string;
  ipa_pronunciation?: string;
  vietnamese_meaning?: string;
  english_definition: string;
  example_sentences?: string[];
  etymology?: Record<string, string>;
  created_at: string;
  updated_at?: string;
}

export interface WordBuildingNote {
  id: string;
  awl_word_id: string;
  note_type: string;
  content: string;
  examples?: string;
  created_at: string;
  updated_at: string;
}

export interface VocabularyQuizTemplate {
  id: string;
  awl_word_id: string;
  question_template: string;
  correct_answer: string;
  target_word_form?: string;
  difficulty_level: number;
  hint_text?: string;
  context_clue?: string;
  created_at: string;
  updated_at: string;
}

export interface WordQuizQuestion {
  id: string;
  questionText: string;
  correctAnswer: string;
  wordForm: string;
  difficulty: number;
  hint?: string;
  context?: string;
  questionType?: 'multiple_choice' | 'fill_blank' | 'true_false';
  options?: string[];
}

export interface WordQuiz {
  wordId: string;
  questions: WordQuizQuestion[];
  totalQuestions: number;
  estimatedTime: number;
}

export interface WordQuizAttempt {
  wordId: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: Array<{
    questionId: string;
    userAnswer: string;
    correctAnswer: string;
    isCorrect: boolean;
    timeSpent: number;
  }>;
}

export interface WordDetails {
  word: AwlWord;
  definitionsByForm: Record<string, WordDefinition[]>;
  buildingNotes: WordBuildingNote[];
  quiz: WordQuiz;
  metadata: {
    lastUpdated: string;
    dataVersion: string;
    completeness: number;
  };
}

export interface WordSearchRequest {
  query: string;
  mode?: 'exact' | 'fuzzy' | 'contains';
  includeWordForms?: boolean;
}

export interface WordSearchResult {
  found: boolean;
  word?: WordDetails;
  suggestions?: string[];
}

export interface VocabularyStats {
  totalWordsStudied: number;
  wordsMastered: number;
  currentStreak: number;
  bestStreak: number;
  totalStudyTime: number;
  averageQuizScore: number;
  totalQuizAttempts: number;
  totalPoints: number;
  currentLevel: number;
  awlCoverage: number;
  favoriteStudyTime?: string;
  mostProductiveDay?: string;
}

export interface SublistProgress {
  sublist: number;
  totalWords: number;
  wordsStudied: number;
  averageScore: number;
  completionPercentage: number;
}

export interface VocabularyDashboard {
  stats: VocabularyStats;
  sublistProgress: SublistProgress[];
  recentWords: unknown[];
  badges: UserBadge[];
  availableBadges: unknown[];
  streaks: unknown[];
  recentSessions: unknown[];
  recommendations: unknown[];
} 