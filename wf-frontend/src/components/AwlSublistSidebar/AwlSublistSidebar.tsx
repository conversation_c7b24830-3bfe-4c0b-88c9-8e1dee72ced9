import React, { useState, useEffect, useCallback } from 'react';
import { ChevronDown, ChevronRight, BookOpen, Search, Loader2, AlertCircle } from 'lucide-react';
import { VocabularyService } from '@/services/vocabularyService';
import type { AwlWord } from '@/types';

interface AwlSublistSidebarProps {
  onWordSelect: (word: string) => void;
  selectedWord?: string;
  className?: string;
  onMobileClose?: () => void;
}

interface SublistData {
  sublist: number;
  words: AwlWord[];
  isExpanded: boolean;
  isLoading: boolean;
}

const AwlSublistSidebar: React.FC<AwlSublistSidebarProps> = ({
  onWordSelect,
  selectedWord,
  className = '',
  onMobileClose
}) => {
  const [sublists, setSublists] = useState<SublistData[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Initialize sublists 1-10
  useEffect(() => {
    const initialSublists: SublistData[] = Array.from({ length: 10 }, (_, i) => ({
      sublist: i + 1,
      words: [],
      isExpanded: false,
      isLoading: false
    }));
    setSublists(initialSublists);
    setIsInitialLoading(false);
  }, []);

  // Load words for a specific sublist
  const loadSublistWords = useCallback(async (sublistNumber: number) => {
    setSublists(prev => prev.map(sub => 
      sub.sublist === sublistNumber 
        ? { ...sub, isLoading: true }
        : sub
    ));

    try {
      // TODO: Implement actual API call to get words by sublist
      // For now, using a mock implementation
      const response = await VocabularyService.getWordsBySublist(sublistNumber);
      
      if (response.data) {
        setSublists(prev => prev.map(sub => 
          sub.sublist === sublistNumber 
            ? { 
                ...sub, 
                words: response.data || [], 
                isLoading: false,
                isExpanded: true 
              }
            : sub
        ));
      } else {
        throw new Error(response.error || 'Failed to load words');
      }
    } catch (err) {
      console.error('Error loading sublist words:', err);
      setError(`Failed to load Sublist ${sublistNumber} words`);
      setSublists(prev => prev.map(sub => 
        sub.sublist === sublistNumber 
          ? { ...sub, isLoading: false }
          : sub
      ));
    }
  }, []);

  // Toggle sublist expansion
  const toggleSublist = useCallback(async (sublistNumber: number) => {
    const sublist = sublists.find(s => s.sublist === sublistNumber);
    if (!sublist) return;

    if (!sublist.isExpanded && sublist.words.length === 0) {
      // Load words if not loaded yet
      await loadSublistWords(sublistNumber);
    } else {
      // Just toggle expansion
      setSublists(prev => prev.map(sub => 
        sub.sublist === sublistNumber 
          ? { ...sub, isExpanded: !sub.isExpanded }
          : sub
      ));
    }
  }, [sublists, loadSublistWords]);

  // Filter words based on search
  const getFilteredWords = useCallback((words: AwlWord[]) => {
    if (!searchQuery.trim()) return words;
    return words.filter(word => 
      word.word.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  // Handle word selection
  const handleWordClick = useCallback((word: string) => {
    onWordSelect(word);
    // Close sidebar on mobile after selection
    if (onMobileClose && window.innerWidth < 1024) {
      setTimeout(() => onMobileClose(), 150); // Small delay to show selection
    }
  }, [onWordSelect, onMobileClose]);

  if (isInitialLoading) {
    return (
      <div className={`bg-white border-l border-gray-200 p-4 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border-l border-gray-200 flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
          <BookOpen className="h-5 w-5 mr-2 text-blue-600" />
          AWL Words
        </h3>
        <p className="text-sm text-gray-600 mb-3">
          Browse words by Academic Word List sublists
        </p>
        
        {/* Search within sidebar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Filter words..."
            className="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <div className="flex items-center text-red-700 text-sm">
            <AlertCircle className="h-4 w-4 mr-2" />
            {error}
          </div>
        </div>
      )}

      {/* Sublists */}
      <div className="flex-1 overflow-y-auto">
        {sublists.map((sublist) => {
          const filteredWords = getFilteredWords(sublist.words);
          const hasMatchingWords = !searchQuery || filteredWords.length > 0;
          
          // Hide sublists that don't have matching words when searching
          if (searchQuery && !hasMatchingWords && sublist.words.length > 0) {
            return null;
          }

          return (
            <div key={sublist.sublist} className="border-b border-gray-100">
              {/* Sublist Header */}
              <button
                onClick={() => toggleSublist(sublist.sublist)}
                className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center">
                  {sublist.isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                  ) : sublist.isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-400 mr-2" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-400 mr-2" />
                  )}
                  <span className="font-medium text-gray-900">
                    Sublist {sublist.sublist}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {sublist.words.length > 0 && (
                    <>
                      {searchQuery && filteredWords.length !== sublist.words.length
                        ? `${filteredWords.length}/${sublist.words.length}`
                        : sublist.words.length
                      } words
                    </>
                  )}
                </div>
              </button>

              {/* Words List */}
              {sublist.isExpanded && (
                <div className="bg-gray-50">
                  {filteredWords.length > 0 ? (
                    <div className="max-h-64 overflow-y-auto">
                      {filteredWords.map((word) => (
                        <button
                          key={word.id}
                          onClick={() => handleWordClick(word.word)}
                          className={`w-full px-6 py-2 text-left hover:bg-blue-50 transition-colors ${
                            selectedWord === word.word
                              ? 'bg-blue-100 text-blue-900 border-r-2 border-blue-500'
                              : 'text-gray-700'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{word.word}</span>
                            <span className="text-xs text-gray-500">
                              #{word.frequency_rank}
                            </span>
                          </div>
                        </button>
                      ))}
                    </div>
                  ) : sublist.words.length > 0 ? (
                    <div className="px-6 py-4 text-sm text-gray-500">
                      No words match &ldquo;{searchQuery}&rdquo;
                    </div>
                  ) : (
                    <div className="px-6 py-4 text-sm text-gray-500">
                      Click to load words for this sublist
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Footer Info */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <p className="text-xs text-gray-600">
          Academic Word List contains 570 word families organized into 10 sublists by frequency.
        </p>
      </div>
    </div>
  );
};

export default AwlSublistSidebar;