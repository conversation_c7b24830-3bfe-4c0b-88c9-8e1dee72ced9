import React, { useState, useCallback, useEffect } from 'react';
import { Volume2, VolumeX, Settings, Play } from 'lucide-react';

interface PronunciationPlayerProps {
  word: string;
  ipa?: string;
  className?: string;
  showIPA?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const PronunciationPlayer: React.FC<PronunciationPlayerProps> = ({
  word,
  ipa,
  className = '',
  showIPA = true,
  size = 'md'
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    rate: 0.8,
    pitch: 1.0,
    volume: 1.0
  });

  // Load available voices
  useEffect(() => {
    const loadVoices = () => {
      const availableVoices = speechSynthesis.getVoices();
      const englishVoices = availableVoices.filter(voice => 
        voice.lang.startsWith('en')
      );
      setVoices(englishVoices);
      
      // Auto-select best quality voice
      const bestVoice = englishVoices.find(voice => 
        voice.name.includes('Enhanced') || 
        voice.name.includes('Premium') || 
        voice.name.includes('Google') ||
        voice.name.includes('Microsoft')
      ) || englishVoices[0];
      
      if (bestVoice) {
        setSelectedVoice(bestVoice.name);
      }
    };

    loadVoices();
    speechSynthesis.addEventListener('voiceschanged', loadVoices);
    
    return () => {
      speechSynthesis.removeEventListener('voiceschanged', loadVoices);
    };
  }, []);

  const handleTTS = useCallback(() => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(word);
      utterance.rate = settings.rate;
      utterance.pitch = settings.pitch;
      utterance.volume = settings.volume;
      
      const voice = voices.find(v => v.name === selectedVoice);
      if (voice) {
        utterance.voice = voice;
      }
      
      utterance.onend = () => setIsPlaying(false);
      utterance.onerror = () => setIsPlaying(false);
      
      speechSynthesis.speak(utterance);
    } else {
      setIsPlaying(false);
      alert('Speech synthesis not supported in this browser');
    }
  }, [word, selectedVoice, settings, voices]);

  const handlePronounce = useCallback(async (useExternalAPI = false) => {
    if (isPlaying) return;

    setIsPlaying(true);
    
    try {
      if (useExternalAPI) {
        // Try external dictionary API for authentic pronunciation
        try {
          const response = await fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${word}`);
          if (response.ok) {
            const data = await response.json();
            const phonetics = data[0]?.phonetics;
            const audioUrl = phonetics?.find((p: { audio?: string }) => p.audio)?.audio;
            
            if (audioUrl) {
              const audio = new Audio(audioUrl);
              audio.onended = () => setIsPlaying(false);
              audio.onerror = () => {
                setIsPlaying(false);
                // Fallback to TTS
                handleTTS();
              };
              await audio.play();
              return;
            }
          }
        } catch {
          console.log('External API failed, falling back to TTS');
        }
      }
      
      // Use browser TTS
      handleTTS();
    } catch (error) {
      console.error('Pronunciation failed:', error);
      setIsPlaying(false);
    }
  }, [word, isPlaying, handleTTS]);

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <div className={`relative inline-flex items-center gap-2 ${className}`}>
      {/* Main Pronounce Button */}
      <button
        onClick={() => handlePronounce(true)}
        disabled={isPlaying}
        className={`flex items-center bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 ${sizeClasses[size]}`}
        title={`Pronounce "${word}"${ipa ? ` - ${ipa}` : ''}`}
      >
        {isPlaying ? (
          <VolumeX className={`${iconSizes[size]} mr-1 animate-pulse`} />
        ) : (
          <Volume2 className={`${iconSizes[size]} mr-1`} />
        )}
        {size !== 'sm' && 'Pronounce'}
      </button>

      {/* IPA Display */}
      {showIPA && ipa && (
        <span className="text-blue-600 font-mono text-sm">
          {ipa}
        </span>
      )}

      {/* Settings Button */}
      {size !== 'sm' && (
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          title="Pronunciation settings"
        >
          <Settings className="h-3 w-3" />
        </button>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="absolute top-full left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-10">
          <h4 className="font-medium text-gray-900 mb-3">Pronunciation Settings</h4>
          
          {/* Voice Selection */}
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Voice
            </label>
            <select
              value={selectedVoice}
              onChange={(e) => setSelectedVoice(e.target.value)}
              className="w-full text-sm border border-gray-300 rounded px-2 py-1"
            >
              {voices.map((voice) => (
                <option key={voice.name} value={voice.name}>
                  {voice.name} ({voice.lang})
                </option>
              ))}
            </select>
          </div>

          {/* Speed Control */}
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Speed: {settings.rate.toFixed(1)}x
            </label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={settings.rate}
              onChange={(e) => setSettings(prev => ({ ...prev, rate: parseFloat(e.target.value) }))}
              className="w-full"
            />
          </div>

          {/* Pitch Control */}
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Pitch: {settings.pitch.toFixed(1)}
            </label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={settings.pitch}
              onChange={(e) => setSettings(prev => ({ ...prev, pitch: parseFloat(e.target.value) }))}
              className="w-full"
            />
          </div>

          {/* Volume Control */}
          <div className="mb-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Volume: {Math.round(settings.volume * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={settings.volume}
              onChange={(e) => setSettings(prev => ({ ...prev, volume: parseFloat(e.target.value) }))}
              className="w-full"
            />
          </div>

          {/* Test Button */}
          <button
            onClick={() => handlePronounce(false)}
            disabled={isPlaying}
            className="w-full flex items-center justify-center px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm disabled:opacity-50"
          >
            <Play className="h-3 w-3 mr-1" />
            Test Pronunciation
          </button>
        </div>
      )}
    </div>
  );
};

export default PronunciationPlayer;