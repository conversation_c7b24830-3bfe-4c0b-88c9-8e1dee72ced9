import { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { BookOpen, LogOut, User, BarChart3, Menu, X, Settings, FileText, Lightbulb, ChevronDown } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';

const TopNavigation = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const { t } = useLanguage();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isVocabularyMenuOpen, setIsVocabularyMenuOpen] = useState(false);
  const settingsRef = useRef<HTMLDivElement>(null);
  const vocabularyRef = useRef<HTMLDivElement>(null);



  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setIsSettingsOpen(false);
      }
      if (vocabularyRef.current && !vocabularyRef.current.contains(event.target as Node)) {
        setIsVocabularyMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get display name from auth context data (no API call needed)
  const getDisplayName = () => {
    // Use auth context data directly
    if (user?.first_name && user?.last_name) {
      return `${user.first_name} ${user.last_name}`.trim();
    }

    // Fallback to email or default
    return user?.email || 'User';
  };



  const isActive = (path: string) => {
    // Handle quiz-results page first to avoid conflicts
    if (location.pathname === '/quiz-results') {
      const navigationSource = location.state?.source;
      if (path === '/dashboard' && navigationSource === 'progress') {
        return true;
      }
      if (path === '/practice' && navigationSource !== 'progress') {
        return true;
      }
      return false;
    }

    if (path === '/practice') {
      // Highlight Practice for practice quiz pages and legacy /home routes
      return location.pathname === '/practice' ||
             location.pathname === '/home' ||
             location.pathname.startsWith('/pre-quiz') ||
             (location.pathname.startsWith('/quiz') && location.search.includes('mode=practice'));
    }

    if (path === '/assessment') {
      // Highlight Assessment for assessment page and test quiz pages
      return location.pathname === '/assessment' ||
             (location.pathname.startsWith('/quiz') && location.search.includes('mode=test'));
    }

    if (path === '/dashboard') {
      // Highlight Dashboard for dashboard, legacy progress, and legacy vocabulary-builder routes
      return location.pathname === '/dashboard' ||
             location.pathname === '/progress' ||
             location.pathname === '/vocabulary-builder';
    }

    return location.pathname === path;
  };

  const navItems = [
    { path: '/practice', label: 'Practice', icon: null },
    { path: '/assessment', label: t('nav.assessment', 'Assessment'), icon: FileText },
    { path: '/dashboard', label: 'Dashboard', icon: BarChart3 },
  ];

  const handleLogout = async () => {
    await signOut();
    navigate('/signin');
    setIsMobileMenuOpen(false);
    setIsSettingsOpen(false);
    setIsVocabularyMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    setIsSettingsOpen(false);
    setIsVocabularyMenuOpen(false);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const toggleSettings = () => {
    setIsSettingsOpen(!isSettingsOpen);
    setIsVocabularyMenuOpen(false);
  };

  const toggleVocabularyMenu = () => {
    setIsVocabularyMenuOpen(!isVocabularyMenuOpen);
    setIsSettingsOpen(false);
  };

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link to="/home" className="flex items-center" onClick={closeMobileMenu}>
              <div className="bg-blue-600 p-2 rounded-lg mr-2 sm:mr-3">
                <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">EnglishQuiz Pro</h1>
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <nav className="flex items-center space-x-6">
              {navItems.map(({ path, label, icon: Icon }) => (
                <Link
                  key={path}
                  to={path}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive(path)
                      ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  {Icon && <Icon className="h-4 w-4 mr-2" />}
                  {label}
                </Link>
              ))}

              {/* Tools Dropdown */}
              <div className="relative" ref={vocabularyRef}>
                <button
                  onClick={toggleVocabularyMenu}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    location.pathname.startsWith('/vocabulary-builder') || isVocabularyMenuOpen
                      ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <Lightbulb className="h-4 w-4 mr-2" />
                  Tools
                  <ChevronDown className="h-4 w-4 ml-1" />
                </button>

                {isVocabularyMenuOpen && (
                  <div className="absolute left-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                    <div className="p-2">
                      <Link
                        to="/vocabulary-builder/awl-highlighter"
                        onClick={() => setIsVocabularyMenuOpen(false)}
                        className="w-full flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        AWL Highlighter
                      </Link>
                      <Link
                        to="/vocabulary-builder/word-master"
                        onClick={() => setIsVocabularyMenuOpen(false)}
                        className="w-full flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                      >
                        <BookOpen className="h-4 w-4 mr-2" />
                        Word Master
                      </Link>
                    </div>
                  </div>
                )}
              </div>

              {/* Settings Dropdown */}
              <div className="relative" ref={settingsRef}>
                <button
                  onClick={toggleSettings}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isSettingsOpen
                      ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  {t('nav.settings')}
                </button>

                {isSettingsOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                    <div className="p-2">
                      {/* User Name - Clickable to Profile */}
                      <Link
                        to="/profile"
                        onClick={() => setIsSettingsOpen(false)}
                        className="w-full flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 border-b border-gray-200 dark:border-gray-700 mb-2"
                      >
                        <User className="h-4 w-4 mr-2" />
                        {getDisplayName()}
                      </Link>

                      {/* Logout */}
                      <button
                        onClick={handleLogout}
                        className="w-full flex items-center px-3 py-2 rounded-md text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        {t('nav.logout')}
                      </button>
                    </div>
                  </div>
                )}
              </div>


            </nav>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              aria-label="Toggle menu"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navItems.map(({ path, label, icon: Icon }) => (
                <Link
                  key={path}
                  to={path}
                  onClick={closeMobileMenu}
                  className={`flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                    isActive(path)
                      ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  {Icon && <Icon className="h-5 w-5 mr-3" />}
                  {label}
                </Link>
              ))}

              {/* Tools Section */}
              <div className="space-y-1">
                <div className="flex items-center px-3 py-2 text-base font-medium text-gray-900 dark:text-white">
                  <Lightbulb className="h-5 w-5 mr-3" />
                  Tools
                </div>
                <div className="pl-6 space-y-1">
                  <Link
                    to="/vocabulary-builder/awl-highlighter"
                    onClick={closeMobileMenu}
                    className={`flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                      location.pathname === '/vocabulary-builder/awl-highlighter'
                        ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                  >
                    <FileText className="h-5 w-5 mr-3" />
                    AWL Highlighter
                  </Link>
                  <Link
                    to="/vocabulary-builder/word-master"
                    onClick={closeMobileMenu}
                    className={`flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                      location.pathname === '/vocabulary-builder/word-master'
                        ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                  >
                    <BookOpen className="h-5 w-5 mr-3" />
                    Word Master
                  </Link>
                </div>
              </div>

              <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                {/* User Name - Clickable to Profile */}
                <Link
                  to="/profile"
                  onClick={closeMobileMenu}
                  className="flex items-center px-3 py-2 text-base font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
                >
                  <User className="h-5 w-5 mr-3" />
                  {getDisplayName()}
                </Link>



                {/* Logout */}
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center px-3 py-2 rounded-md text-base font-medium text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                >
                  <LogOut className="h-5 w-5 mr-3" />
                  {t('nav.logout')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default TopNavigation;
