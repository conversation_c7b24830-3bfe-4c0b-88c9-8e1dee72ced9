import React from 'react';
import type { QuizQuestionProps } from '@/types';
import { useLanguage } from '@/context/LanguageContext';

/**
 * QuizQuestion component that displays a single quiz question with options
 * Supports both practice and test modes with different feedback styles
 */
const QuizQuestion: React.FC<QuizQuestionProps> = ({
  question,
  selectedAnswer,
  onAnswerSelect,
  showFeedback = false,
  mode,
  questionNumber,
  totalQuestions
}) => {
  const { t } = useLanguage();
  const isCorrect = selectedAnswer === question.correct_answer;
  const showCorrectAnswer = showFeedback && mode === 'practice';

  // Use the options from the question
  const displayOptions = question.options || [];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      {/* Question Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
            {t('quiz.question')} {questionNumber} {t('quiz.of')} {totalQuestions}
          </span>
          {showFeedback && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              isCorrect
                ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200'
            }`}>
              {isCorrect ? t('quiz.correct') : t('quiz.incorrect')}
            </span>
          )}
        </div>
        

        
        {/* Question Text */}
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white leading-relaxed">
          {question.question_text}
        </h3>
      </div>

      {/* Answer Options */}
      <div className="space-y-3 mb-6">
        {displayOptions.map((option) => {
          const optionText = option.option_text;
          const isSelected = selectedAnswer === optionText;
          const isCorrectOption = optionText === question.correct_answer;
          
          let optionClasses = 'w-full p-4 text-left border-2 rounded-lg transition-all duration-200 ';
          
          if (showCorrectAnswer) {
            // Practice mode with feedback
            if (isCorrectOption) {
              optionClasses += 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-200';
            } else if (isSelected && !isCorrectOption) {
              optionClasses += 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-900 dark:text-red-200';
            } else {
              optionClasses += 'border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300';
            }
          } else {
            // No feedback or test mode
            if (isSelected) {
              optionClasses += 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-200';
            } else {
              optionClasses += 'border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:border-gray-300 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700';
            }
          }

          return (
            <button
              key={option.id}
              onClick={() => onAnswerSelect(optionText)}
              disabled={showFeedback && mode === 'practice'}
              className={optionClasses}
            >
              <div className="flex items-center">
                <span className="flex-shrink-0 w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center text-sm font-medium">
                  {option.option_key}
                </span>
                <span className="flex-1 text-left">{optionText}</span>
                {showCorrectAnswer && isCorrectOption && (
                  <svg className="w-5 h-5 text-green-600 ml-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
                {showCorrectAnswer && isSelected && !isCorrectOption && (
                  <svg className="w-5 h-5 text-red-600 ml-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </button>
          );
        })}
      </div>

      {/* Explanation (Practice mode only) */}
      {showFeedback && mode === 'practice' && question.explanations && question.explanations.length > 0 && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">{t('quiz.explanation')}</h4>
          <div className="space-y-2">
            {question.explanations.map((explanation, index) => (
              <div key={explanation.id || index} className="text-blue-800 text-sm leading-relaxed">
                <div className="font-medium mb-1">
                  {explanation.explanation_type === 'correct_answer' ? `✅ ${t('quiz.correctAnswer')}:` : `❌ ${t('quiz.whyWrong')}:`}
                </div>
                <div>{explanation.content}</div>
                {(explanation.metadata as { rule?: string })?.rule && (
                  <div className="text-xs text-blue-600 mt-1 italic">
                    {t('quiz.rule')}: {(explanation.metadata as { rule?: string }).rule}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuizQuestion; 