import { useState, useEffect, useCallback } from 'react';
import type { WordQuiz } from '@/types';

interface QuizAnswer {
  questionId: string;
  userAnswer: string;
  correctAnswer: string;
  isCorrect: boolean;
  timeSpent: number;
}

interface QuizAttempt {
  wordId: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: QuizAnswer[];
}

/**
 * <PERSON><PERSON> hook for WordQuiz component
 * Manages quiz state, question navigation, scoring, and completion
 */
export const useWordQuiz = (
  quiz: WordQuiz,
  onComplete: (attempt: QuizAttempt) => void
) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [answers, setAnswers] = useState<QuizAnswer[]>([]);
  const [isAnswered, setIsAnswered] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [startTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [timeSpent, setTimeSpent] = useState(0);
  const [isQuizComplete, setIsQuizComplete] = useState(false);

  // Update timer every second
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  // Reset question state when question changes
  useEffect(() => {
    setUserAnswer('');
    setSelectedOption(null);
    setIsAnswered(false);
    setShowExplanation(false);
    setQuestionStartTime(Date.now());
  }, [currentQuestionIndex]);

  /**
   * Get current question
   */
  const getCurrentQuestion = useCallback(() => {
    return quiz.questions[currentQuestionIndex] || null;
  }, [quiz.questions, currentQuestionIndex]);

  /**
   * Calculate progress percentage
   */
  const getProgressPercentage = useCallback(() => {
    return ((currentQuestionIndex + (isAnswered ? 1 : 0)) / quiz.totalQuestions) * 100;
  }, [currentQuestionIndex, isAnswered, quiz.totalQuestions]);

  /**
   * Check if answer is correct
   */
  const isCorrect = useCallback(() => {
    const currentQuestion = getCurrentQuestion();
    if (!currentQuestion || !currentQuestion.correctAnswer) return false;

    const correctAnswer = currentQuestion.correctAnswer.toLowerCase().trim();
    
    // For fill-in-blank questions
    if (currentQuestion.questionType === 'fill_blank' || 
        currentQuestion.questionText.includes('Complete:') || 
        currentQuestion.questionText.includes('Fill') ||
        currentQuestion.questionText.includes('_____')) {
      if (userAnswer) {
        return userAnswer.toLowerCase().trim() === correctAnswer;
      }
      return false;
    }
    
    // For multiple choice questions
    if (selectedOption && currentQuestion.options) {
      const selectedIndex = selectedOption.charCodeAt(0) - 65; // Convert A,B,C,D to 0,1,2,3
      const selectedAnswerText = currentQuestion.options[selectedIndex];
      return selectedAnswerText && selectedAnswerText.toLowerCase().trim() === correctAnswer;
    }
    
    return false;
  }, [userAnswer, selectedOption, getCurrentQuestion]);

  /**
   * Calculate current score
   */
  const score = useCallback(() => {
    return answers.filter(answer => answer.isCorrect).length;
  }, [answers]);

  /**
   * Submit current answer
   */
  const handleSubmitAnswer = useCallback(() => {
    const currentQuestion = getCurrentQuestion();
    if (!currentQuestion || !currentQuestion.correctAnswer || isAnswered) return;

    const questionTimeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
    const answerIsCorrect = isCorrect();
    
    const answer: QuizAnswer = {
      questionId: currentQuestion.id,
      userAnswer: userAnswer || selectedOption || '',
      correctAnswer: currentQuestion.correctAnswer || '',
      isCorrect: Boolean(answerIsCorrect),
      timeSpent: questionTimeSpent
    };

    setAnswers(prev => [...prev, answer]);
    setIsAnswered(true);
    setShowExplanation(true);
  }, [getCurrentQuestion, isAnswered, questionStartTime, isCorrect, userAnswer, selectedOption]);

  /**
   * Navigate to next question
   */
  const handleNextQuestion = useCallback(() => {
    if (currentQuestionIndex < quiz.totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  }, [currentQuestionIndex, quiz.totalQuestions]);

  /**
   * Navigate to previous question
   */
  const handlePreviousQuestion = useCallback(() => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      
      // Restore previous answer state
      const previousAnswer = answers[currentQuestionIndex - 1];
      if (previousAnswer) {
        setUserAnswer(previousAnswer.userAnswer);
        setIsAnswered(true);
        setShowExplanation(true);
      }
    }
  }, [currentQuestionIndex, answers]);

  /**
   * Complete the quiz
   */
  const handleCompleteQuiz = useCallback(() => {
    const finalScore = score();
    const totalTimeSpent = Math.floor((Date.now() - startTime) / 1000);
    
    const attempt: QuizAttempt = {
      wordId: quiz.wordId,
      score: finalScore,
      totalQuestions: quiz.totalQuestions,
      correctAnswers: finalScore,
      timeSpent: totalTimeSpent,
      answers: answers
    };

    setIsQuizComplete(true);
    onComplete(attempt);
  }, [score, startTime, quiz.wordId, quiz.totalQuestions, answers, onComplete]);

  /**
   * Navigation helpers
   */
  const canGoNext = currentQuestionIndex < quiz.totalQuestions - 1;
  const canGoPrevious = currentQuestionIndex > 0;

  /**
   * Auto-advance to next question after showing explanation
   */
  useEffect(() => {
    if (showExplanation && isAnswered) {
      // Optional: Auto-advance after delay
      // const timer = setTimeout(() => {
      //   if (canGoNext) {
      //     handleNextQuestion();
      //   } else {
      //     handleCompleteQuiz();
      //   }
      // }, 3000);
      // 
      // return () => clearTimeout(timer);
    }
  }, [showExplanation, isAnswered, canGoNext, handleNextQuestion, handleCompleteQuiz]);

  return {
    // State
    currentQuestionIndex,
    userAnswer,
    setUserAnswer,
    selectedOption,
    setSelectedOption,
    showExplanation,
    isAnswered,
    isCorrect: isCorrect(),
    score: score(),
    timeSpent,
    answers,
    isQuizComplete,
    
    // Actions
    handleSubmitAnswer,
    handleNextQuestion,
    handlePreviousQuestion,
    handleCompleteQuiz,
    
    // Computed values
    getCurrentQuestion,
    getProgressPercentage: getProgressPercentage(),
    canGoNext,
    canGoPrevious
  };
};