import React from 'react';
import { Check<PERSON>ircle, XCircle, Clock, ArrowRight, ArrowLeft, Award, Target, Lightbulb } from 'lucide-react';
import { useWordQuiz } from './WordQuiz.handler';
import type { WordQuiz, WordDetails } from '@/types';

interface WordQuizProps {
  quiz: WordQuiz;
  wordDetails: WordDetails;
  onComplete: (attempt: {
    wordId: string;
    score: number;
    totalQuestions: number;
    correctAnswers: number;
    timeSpent: number;
    answers: Array<{
      questionId: string;
      userAnswer: string;
      correctAnswer: string;
      isCorrect: boolean;
      timeSpent: number;
    }>;
  }) => void;
  onBack: () => void;
}

const WordQuiz: React.FC<WordQuizProps> = ({ quiz, wordDetails, onComplete, onBack }) => {
  const {
    currentQuestionIndex,
    userAnswer,
    setUserAnswer,
    selectedOption,
    setSelectedOption,
    showExplanation,
    isAnswered,
    isCorrect,
    score,
    timeSpent,
    handleNextQuestion,
    handlePreviousQuestion,
    handleSubmitAnswer,
    handleCompleteQuiz,
    getCurrentQuestion,
    getProgressPercentage,
    canGoNext,
    canGoPrevious,
    isQuizComplete
  } = useWordQuiz(quiz, onComplete);

  const currentQuestion = getCurrentQuestion();

  if (!currentQuestion) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
        <p className="text-gray-600">No questions available for this quiz.</p>
        <button
          onClick={onBack}
          className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Back to Word Details
        </button>
      </div>
    );
  }

  if (isQuizComplete) {
    const percentage = Math.round((score / quiz.totalQuestions) * 100);
    const passed = percentage >= 70;

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="text-center">
          <div className={`w-20 h-20 rounded-full mx-auto mb-6 flex items-center justify-center ${
            passed ? 'bg-green-100' : 'bg-yellow-100'
          }`}>
            <Award className={`h-10 w-10 ${passed ? 'text-green-600' : 'text-yellow-600'}`} />
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Quiz Complete!</h2>
          <p className="text-gray-600 mb-6">
            You completed the quiz for &ldquo;{wordDetails.word.word}&rdquo;
          </p>
          
          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className={`text-3xl font-bold ${passed ? 'text-green-600' : 'text-yellow-600'}`}>
                  {percentage}%
                </div>
                <p className="text-gray-600 text-sm">Score</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-600">
                  {score}/{quiz.totalQuestions}
                </div>
                <p className="text-gray-600 text-sm">Correct</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600">
                  {Math.floor(timeSpent / 60)}m {timeSpent % 60}s
                </div>
                <p className="text-gray-600 text-sm">Time</p>
              </div>
              <div>
                <div className={`text-3xl font-bold ${passed ? 'text-green-600' : 'text-red-600'}`}>
                  {passed ? 'PASS' : 'RETRY'}
                </div>
                <p className="text-gray-600 text-sm">Result</p>
              </div>
            </div>
          </div>

          {passed ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <p className="text-green-800">
                <strong>Excellent!</strong> You have demonstrated good understanding of &ldquo;{wordDetails.word.word}&rdquo;. 
                The word has been marked as studied in your progress.
              </p>
            </div>
          ) : (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-yellow-800">
                <strong>Good effort!</strong> Consider reviewing the word definitions and examples before trying again. 
                Practice makes perfect!
              </p>
            </div>
          )}

          <div className="flex gap-3 justify-center">
            <button
              onClick={onBack}
              className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Back to Word Details
            </button>
            {!passed && (
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Quiz Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Target className="h-5 w-5 mr-2 text-green-600" />
            Quiz: &ldquo;{wordDetails.word.word}&rdquo;
          </h2>
          <p className="text-gray-600 text-sm">
            Question {currentQuestionIndex + 1} of {quiz.totalQuestions}
          </p>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <Clock className="h-4 w-4 mr-1" />
          {Math.floor(timeSpent / 60)}:{(timeSpent % 60).toString().padStart(2, '0')}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>Progress</span>
          <span>{Math.round(getProgressPercentage)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-green-600 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${getProgressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Question */}
      <div className="mb-8">
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex-1">
            {currentQuestion.questionText}
          </h3>
          {currentQuestion.difficulty && (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ml-4 ${
              currentQuestion.difficulty === 1 
                ? 'bg-green-100 text-green-800' 
                : currentQuestion.difficulty === 2 
                ? 'bg-yellow-100 text-yellow-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {currentQuestion.difficulty === 1 ? 'Easy' : currentQuestion.difficulty === 2 ? 'Medium' : 'Hard'}
            </span>
          )}
        </div>

        {currentQuestion.context && (
          <p className="text-gray-600 text-sm mb-4 italic">
            Context: {currentQuestion.context}
          </p>
        )}

        {/* Answer Input */}
        <div className="space-y-3">
          {(currentQuestion.questionType === 'fill_blank' || 
            currentQuestion.questionText.includes('Complete:') || 
            currentQuestion.questionText.includes('Fill') ||
            currentQuestion.questionText.includes('_____')) ? (
            // Fill-in-the-blank question
            <div>
              <input
                type="text"
                value={userAnswer}
                onChange={(e) => setUserAnswer(e.target.value)}
                placeholder="Type your answer here..."
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                disabled={isAnswered}
                onKeyPress={(e) => e.key === 'Enter' && !isAnswered && handleSubmitAnswer()}
              />
            </div>
          ) : (
            // Multiple choice question
            <div className="space-y-2">
              {(currentQuestion.options || [currentQuestion.correctAnswer, 'Option B (placeholder)', 'Option C (placeholder)', 'Option D (placeholder)']).map((optionText, index) => {
                const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
                const isCorrectOption = optionText === currentQuestion.correctAnswer;
                
                return (
                  <button
                    key={optionLetter}
                    onClick={() => !isAnswered && setSelectedOption(optionLetter)}
                    disabled={isAnswered}
                    className={`w-full text-left p-4 border rounded-lg transition-colors ${
                      selectedOption === optionLetter
                        ? isAnswered
                          ? isCorrectOption
                            ? 'bg-green-100 border-green-500 text-green-800'
                            : 'bg-red-100 border-red-500 text-red-800'
                          : 'bg-blue-100 border-blue-500'
                        : isAnswered && isCorrectOption
                          ? 'bg-green-50 border-green-300'
                          : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="font-semibold mr-3">{optionLetter}.</span>
                      <span className="flex-1">{optionText}</span>
                      {isAnswered && (
                        <span className="ml-auto">
                          {selectedOption === optionLetter && (
                            isCorrectOption ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : (
                              <XCircle className="h-5 w-5 text-red-600" />
                            )
                          )}
                          {selectedOption !== optionLetter && isCorrectOption && (
                            <CheckCircle className="h-5 w-5 text-green-400" />
                          )}
                        </span>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          )}
        </div>

        {/* Hint */}
        {currentQuestion.hint && !isAnswered && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start">
              <Lightbulb className="h-4 w-4 text-blue-600 mr-2 mt-0.5" />
              <div>
                <p className="text-blue-800 text-sm font-medium">Hint:</p>
                <p className="text-blue-700 text-sm">{currentQuestion.hint}</p>
              </div>
            </div>
          </div>
        )}

        {/* Explanation */}
        {showExplanation && isAnswered && (
          <div className={`mt-4 p-4 rounded-lg border ${
            isCorrect 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-start">
              {isCorrect ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mr-2 mt-0.5" />
              )}
              <div className="flex-1">
                <p className={`font-medium text-sm ${
                  isCorrect ? 'text-green-800' : 'text-red-800'
                }`}>
                  {isCorrect ? 'Correct!' : 'Incorrect'}
                </p>
                <p className={`text-sm ${
                  isCorrect ? 'text-green-700' : 'text-red-700'
                }`}>
                  The correct answer is: <strong>{currentQuestion.correctAnswer}</strong>
                </p>
                {currentQuestion.context && (
                  <p className={`text-sm mt-1 ${
                    isCorrect ? 'text-green-600' : 'text-red-600'
                  }`}>
                    Used in {currentQuestion.context.toLowerCase()} contexts.
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quiz Controls */}
      <div className="flex items-center justify-between">
        <button
          onClick={handlePreviousQuestion}
          disabled={!canGoPrevious}
          className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Previous
        </button>

        <div className="flex gap-3">
          {!isAnswered && (
            <button
              onClick={handleSubmitAnswer}
              disabled={
                (currentQuestion.questionType === 'fill_blank' || 
                 currentQuestion.questionText.includes('Complete:') || 
                 currentQuestion.questionText.includes('Fill') ||
                 currentQuestion.questionText.includes('_____'))
                  ? !userAnswer.trim()
                  : !selectedOption
              }
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              Submit Answer
            </button>
          )}

          {isAnswered && (
            <>
              {canGoNext ? (
                <button
                  onClick={handleNextQuestion}
                  className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Next Question
                  <ArrowRight className="h-4 w-4 ml-1" />
                </button>
              ) : (
                <button
                  onClick={handleCompleteQuiz}
                  className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Complete Quiz
                  <Award className="h-4 w-4 ml-1" />
                </button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default WordQuiz;