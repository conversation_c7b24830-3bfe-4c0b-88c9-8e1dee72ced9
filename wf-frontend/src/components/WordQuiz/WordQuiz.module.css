/* WordQuiz component styles */

.quizContainer {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.quizHeader {
  @apply flex items-center justify-between mb-6;
}

.questionSection {
  @apply mb-8;
}

.questionText {
  @apply text-lg font-medium text-gray-900 flex-1;
}

.difficultyBadge {
  @apply px-2 py-1 rounded-full text-xs font-medium ml-4;
}

.difficultyEasy {
  @apply bg-green-100 text-green-800;
}

.difficultyMedium {
  @apply bg-yellow-100 text-yellow-800;
}

.difficultyHard {
  @apply bg-red-100 text-red-800;
}

.answerInput {
  @apply w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent;
}

.answerInput:disabled {
  @apply bg-gray-50 cursor-not-allowed;
}

.multipleChoiceOptions {
  @apply space-y-2;
}

.optionButton {
  @apply w-full text-left p-4 border rounded-lg transition-colors;
}

.optionButton:not(:disabled):hover {
  @apply bg-gray-50;
}

.optionSelected {
  @apply bg-blue-100 border-blue-500;
}

.optionCorrect {
  @apply bg-green-100 border-green-500 text-green-800;
}

.optionIncorrect {
  @apply bg-red-100 border-red-500 text-red-800;
}

.optionDefault {
  @apply border-gray-300;
}

.hintSection {
  @apply mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg;
}

.explanationSection {
  @apply mt-4 p-4 rounded-lg border;
}

.explanationCorrect {
  @apply bg-green-50 border-green-200;
}

.explanationIncorrect {
  @apply bg-red-50 border-red-200;
}

.progressBar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progressFill {
  @apply bg-green-600 h-2 rounded-full transition-all duration-300;
}

.quizControls {
  @apply flex items-center justify-between;
}

.navigationButton {
  @apply flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors;
}

.submitButton {
  @apply px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors;
}

.nextButton {
  @apply flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors;
}

.completeButton {
  @apply flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors;
}

/* Results screen styles */
.resultsContainer {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-8;
}

.resultsIcon {
  @apply w-20 h-20 rounded-full mx-auto mb-6 flex items-center justify-center;
}

.resultsIconPass {
  @apply bg-green-100;
}

.resultsIconRetry {
  @apply bg-yellow-100;
}

.resultsStats {
  @apply bg-gray-50 rounded-lg p-6 mb-6;
}

.resultsGrid {
  @apply grid grid-cols-2 gap-4 text-center;
}

.statValue {
  @apply text-3xl font-bold;
}

.statLabel {
  @apply text-gray-600 text-sm;
}

.feedbackSection {
  @apply p-4 mb-6 rounded-lg border;
}

.feedbackPass {
  @apply bg-green-50 border-green-200;
}

.feedbackRetry {
  @apply bg-yellow-50 border-yellow-200;
}

.resultActions {
  @apply flex gap-3 justify-center;
}