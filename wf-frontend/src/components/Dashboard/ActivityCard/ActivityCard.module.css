/* ActivityCard Component Styles */

.activityCard {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 transition-all duration-200;
}

.activityCard.clickable {
  @apply hover:shadow-md hover:border-gray-200 dark:hover:border-gray-600 cursor-pointer;
}

.activityContent {
  @apply space-y-3;
}

.activityHeader {
  @apply flex items-center justify-between;
}

.activityMeta {
  @apply flex items-center gap-2;
}

.activityIcon {
  @apply w-4 h-4;
}

.activityIcon.blue {
  @apply text-blue-600;
}

.activityIcon.green {
  @apply text-green-600;
}

.activityIcon.purple {
  @apply text-purple-600;
}

.activityIcon.gray {
  @apply text-gray-600;
}

.activityType {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.activityTime {
  @apply text-xs text-gray-500 dark:text-gray-500;
}

.activityTitle {
  @apply text-base font-semibold text-gray-900 dark:text-white line-clamp-2;
}

.activityDetails {
  @apply space-y-2;
}

.activityDetailText {
  @apply text-sm text-gray-600 dark:text-gray-300;
}

.activityStats {
  @apply flex items-center gap-4;
}

.activityStat {
  @apply flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400;
}

.statIcon {
  @apply w-3 h-3;
}