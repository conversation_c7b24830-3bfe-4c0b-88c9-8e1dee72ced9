import React from 'react';
import { Clock, BookOpen, Trophy, Target } from 'lucide-react';
import type { CombinedLearningActivity } from 'wf-shared/types';
import styles from './ActivityCard.module.css';

export interface ActivityCardProps {
  /** Activity data */
  activity: CombinedLearningActivity;
  
  /** Click handler */
  onClick?: (activity: CombinedLearningActivity) => void;
  
  /** Whether to show detailed information */
  showDetails?: boolean;
  
  /** Custom CSS class */
  className?: string;
}

/**
 * Reusable activity card component for dashboard
 * Displays learning activities with appropriate icons and formatting
 */
export const ActivityCard: React.FC<ActivityCardProps> = ({
  activity,
  onClick,
  showDetails = true,
  className = ''
}) => {
  const cardClasses = `${styles.activityCard} ${className} ${onClick ? styles.clickable : ''}`;
  
  // Format timestamp
  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      if (diffInHours < 1) return 'Just now';
      return `${diffInHours}h ago`;
    }
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return date.toLocaleDateString();
  };

  // Format duration
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  // Get activity icon and type-specific info
  const getActivityInfo = () => {
    switch (activity.type) {
      case 'quiz':
        return {
          icon: Trophy,
          color: 'blue',
          typeLabel: activity.mode === 'practice' ? '🎯 Practice' : '📝 Test',
          details: `${activity.correctAnswers}/${activity.totalQuestions} correct (${activity.score}%)`
        };
      case 'vocabulary':
        return {
          icon: BookOpen,
          color: 'green',
          typeLabel: '📚 Vocabulary',
          details: `${activity.wordsLearned} words studied${activity.sublist ? ` • Sublist ${activity.sublist}` : ''}`
        };
      case 'word_study':
        return {
          icon: Target,
          color: 'purple',
          typeLabel: '🎯 Word Study',
          details: `${activity.wordsLearned} words learned`
        };
      default:
        return {
          icon: BookOpen,
          color: 'gray',
          typeLabel: '📖 Study',
          details: 'Learning activity'
        };
    }
  };

  const { icon: Icon, color, typeLabel, details } = getActivityInfo();
  const iconClasses = `${styles.activityIcon} ${styles[color]}`;

  return (
    <div className={cardClasses} onClick={() => onClick?.(activity)}>
      <div className={styles.activityContent}>
        {/* Header with icon and type */}
        <div className={styles.activityHeader}>
          <div className={styles.activityMeta}>
            <Icon className={iconClasses} />
            <span className={styles.activityType}>{typeLabel}</span>
          </div>
          <span className={styles.activityTime}>{formatTime(activity.timestamp)}</span>
        </div>

        {/* Title */}
        <h4 className={styles.activityTitle}>{activity.title}</h4>

        {/* Details */}
        {showDetails && (
          <div className={styles.activityDetails}>
            <span className={styles.activityDetailText}>{details}</span>
            
            <div className={styles.activityStats}>
              {activity.timeSpent && (
                <span className={styles.activityStat}>
                  <Clock className={styles.statIcon} />
                  {formatDuration(activity.timeSpent)}
                </span>
              )}
              
              {activity.points && (
                <span className={styles.activityStat}>
                  <Trophy className={styles.statIcon} />
                  {activity.points} pts
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};