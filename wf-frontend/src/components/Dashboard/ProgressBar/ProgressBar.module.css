/* ProgressBar Component Styles */

.progressContainer {
  @apply space-y-2;
}

.progressHeader {
  @apply flex items-center justify-between;
}

.progressLabel {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.progressValues {
  @apply flex items-center gap-2;
}

.progressPercentage {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

.progressText {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

/* Progress bar container */
.progressBar {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden transition-all duration-300;
}

/* Size variants */
.progressBar.sm {
  @apply h-2;
}

.progressBar.md {
  @apply h-3;
}

.progressBar.lg {
  @apply h-4;
}

/* Progress fill */
.progressFill {
  @apply h-full transition-all duration-500 ease-out;
}

/* Color variants for fill */
.blueFill {
  @apply bg-blue-600;
}

.greenFill {
  @apply bg-green-600;
}

.orangeFill {
  @apply bg-orange-600;
}

.purpleFill {
  @apply bg-purple-600;
}

.redFill {
  @apply bg-red-600;
}

.progressSubtitle {
  @apply text-xs text-gray-500 dark:text-gray-400;
}