import React from 'react';
import styles from './ProgressBar.module.css';

export interface ProgressBarProps {
  /** Current progress value */
  value: number;
  
  /** Maximum value (defaults to 100) */
  max?: number;
  
  /** Label to display above the progress bar */
  label?: string;
  
  /** Text to display below the progress bar */
  subtitle?: string;
  
  /** Color theme for the progress bar */
  color?: 'blue' | 'green' | 'orange' | 'purple' | 'red';
  
  /** Size variant */
  size?: 'sm' | 'md' | 'lg';
  
  /** Whether to show percentage text */
  showPercentage?: boolean;
  
  /** Whether to show current/max values */
  showValues?: boolean;
  
  /** Custom CSS class */
  className?: string;
}

/**
 * Reusable progress bar component for dashboard metrics
 * Used for showing completion percentages, goals, etc.
 */
export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  label,
  subtitle,
  color = 'blue',
  size = 'md',
  showPercentage = false,
  showValues = false,
  className = ''
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  
  const containerClasses = `${styles.progressContainer} ${className}`;
  const barClasses = `${styles.progressBar} ${styles[color]} ${styles[size]}`;
  const fillClasses = `${styles.progressFill} ${styles[`${color}Fill`]}`;

  return (
    <div className={containerClasses}>
      {(label || showPercentage || showValues) && (
        <div className={styles.progressHeader}>
          {label && (
            <span className={styles.progressLabel}>{label}</span>
          )}
          
          <div className={styles.progressValues}>
            {showPercentage && (
              <span className={styles.progressPercentage}>
                {percentage.toFixed(0)}%
              </span>
            )}
            
            {showValues && (
              <span className={styles.progressText}>
                {value}/{max}
              </span>
            )}
          </div>
        </div>
      )}
      
      <div className={barClasses}>
        <div 
          className={fillClasses}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {subtitle && (
        <div className={styles.progressSubtitle}>{subtitle}</div>
      )}
    </div>
  );
};