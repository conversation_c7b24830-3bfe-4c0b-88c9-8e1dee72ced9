import React from 'react';
import { LucideIcon } from 'lucide-react';
import styles from './StatCard.module.css';

export interface StatCardProps {
  /** The main label for the statistic */
  label: string;
  
  /** The primary value to display */
  value: string | number;
  
  /** Optional subtext below the value */
  subtext?: string;
  
  /** Optional motivation or additional text */
  motivationText?: string;
  
  /** Icon to display */
  icon: LucideIcon;
  
  /** Icon color theme */
  iconColor?: 'blue' | 'green' | 'orange' | 'purple' | 'red';
  
  /** Trend indicator */
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    value: string;
    label?: string;
  };
  
  /** Optional click handler */
  onClick?: () => void;
  
  /** Custom CSS class */
  className?: string;
}

/**
 * Reusable stat card component for dashboard metrics
 * Used across Overview, Performance, and Activity tabs
 */
export const StatCard: React.FC<StatCardProps> = ({
  label,
  value,
  subtext,
  motivationText,
  icon: Icon,
  iconColor = 'blue',
  trend,
  onClick,
  className = ''
}) => {
  const cardClasses = `${styles.statCard} ${className} ${onClick ? styles.clickable : ''}`;
  const iconClasses = `${styles.statIcon} ${styles[iconColor]}`;

  return (
    <div className={cardClasses} onClick={onClick}>
      <div className={styles.statCardContent}>
        <div className={styles.statCardHeader}>
          <Icon className={iconClasses} />
          <span className={styles.statLabel}>{label}</span>
        </div>
        
        <div className={styles.statValue}>{value}</div>
        
        {subtext && (
          <div className={styles.statSubtext}>{subtext}</div>
        )}
        
        {trend && (
          <div className={styles.trendContainer}>
            {trend.direction === 'up' && (
              <span className={styles.trendPositive}>
                ↗ {trend.value} {trend.label || ''}
              </span>
            )}
            {trend.direction === 'down' && (
              <span className={styles.trendNegative}>
                ↘ {trend.value} {trend.label || ''}
              </span>
            )}
            {trend.direction === 'neutral' && (
              <span className={styles.trendNeutral}>
                → {trend.value} {trend.label || ''}
              </span>
            )}
          </div>
        )}
        
        {motivationText && (
          <div className={styles.motivationText}>
            {motivationText}
          </div>
        )}
      </div>
    </div>
  );
};