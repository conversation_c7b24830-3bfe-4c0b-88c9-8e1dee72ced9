/* StatCard Component Styles */

.statCard {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 transition-all duration-200;
}

.statCard.clickable {
  @apply hover:shadow-md hover:border-gray-200 dark:hover:border-gray-600 cursor-pointer;
}

.statCardContent {
  @apply space-y-3;
}

.statCardHeader {
  @apply flex items-center gap-3;
}

.statIcon {
  @apply w-6 h-6 transition-colors;
}

.statIcon.blue {
  @apply text-blue-600;
}

.statIcon.green {
  @apply text-green-600;
}

.statIcon.orange {
  @apply text-orange-600;
}

.statIcon.purple {
  @apply text-purple-600;
}

.statIcon.red {
  @apply text-red-600;
}

.statLabel {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.statValue {
  @apply text-3xl font-bold text-gray-900 dark:text-white;
}

.statSubtext {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.trendContainer {
  @apply mt-2;
}

.trendPositive {
  @apply text-sm font-medium text-green-600 dark:text-green-400;
}

.trendNegative {
  @apply text-sm font-medium text-red-600 dark:text-red-400;
}

.trendNeutral {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.motivationText {
  @apply text-sm font-medium text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20 px-3 py-2 rounded-lg text-center;
}