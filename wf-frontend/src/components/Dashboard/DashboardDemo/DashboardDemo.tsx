import React from 'react';
import { 
  <PERSON>, 
  Target, 
  BookOpen, 
  Trophy, 
  Clock, 
  TrendingUp,
  Calendar
} from 'lucide-react';
import { StatCard, ProgressBar, ActivityCard, DashboardSection } from '../';
import type { CombinedLearningActivity } from 'wf-shared/types';

/**
 * Demo component showcasing all shared dashboard components
 * This demonstrates the reusable components in action
 */
export const DashboardDemo: React.FC = () => {
  // Mock activity data
  const sampleActivity: CombinedLearningActivity = {
    id: 'demo-1',
    type: 'quiz',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    title: 'Advanced Grammar Quiz',
    score: 88,
    correctAnswers: 22,
    totalQuestions: 25,
    mode: 'practice',
    timeSpent: 900, // 15 minutes
    points: 150
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Dashboard Components Demo
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Reusable components for the unified learning dashboard
          </p>
        </div>

        {/* StatCard Showcase */}
        <DashboardSection
          title="StatCard Examples"
          subtitle="Versatile metric display cards with icons, trends, and motivational text"
          icon={TrendingUp}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              label="Current Streak"
              value={7}
              subtext="days in a row"
              motivationText="🔥 On fire!"
              icon={Flame}
              iconColor="orange"
            />

            <StatCard
              label="Average Score"
              value="85.3%"
              subtext="last 10 quizzes"
              icon={Target}
              iconColor="blue"
              trend={{
                direction: 'up',
                value: '+4.2%',
                label: 'this week'
              }}
            />

            <StatCard
              label="Words Mastered"
              value={248}
              subtext="of 312 studied"
              icon={BookOpen}
              iconColor="green"
              trend={{
                direction: 'up',
                value: '+12',
                label: 'this week'
              }}
            />

            <StatCard
              label="Study Time"
              value="4h 32m"
              subtext="this week"
              icon={Clock}
              iconColor="purple"
              trend={{
                direction: 'down',
                value: '-23m',
                label: 'vs last week'
              }}
            />
          </div>
        </DashboardSection>

        {/* ProgressBar Showcase */}
        <DashboardSection
          title="ProgressBar Examples"
          subtitle="Flexible progress indicators with various configurations"
          icon={Target}
        >
          <div className="space-y-6">
            <ProgressBar
              label="Weekly Goal Progress"
              value={8}
              max={15}
              showPercentage
              showValues
              color="blue"
              size="lg"
              subtitle="7 more activities to reach your weekly goal"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ProgressBar
                label="AWL Coverage"
                value={43}
                max={100}
                showPercentage
                color="green"
                size="md"
                subtitle="247 of 570 words studied"
              />

              <ProgressBar
                label="Sublist 1 Mastery"
                value={52}
                max={60}
                showValues
                color="purple"
                size="md"
                subtitle="8 words remaining"
              />
            </div>

            <ProgressBar
              label="Daily Goal"
              value={3}
              max={5}
              showValues
              color="orange"
              size="sm"
              subtitle="Complete 2 more activities today"
            />
          </div>
        </DashboardSection>

        {/* ActivityCard Showcase */}
        <DashboardSection
          title="ActivityCard Examples"
          subtitle="Rich activity display cards for different learning types"
          icon={Calendar}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <ActivityCard
              activity={sampleActivity}
              onClick={(activity) => console.log('Clicked activity:', activity)}
            />

            <ActivityCard
              activity={{
                id: 'demo-2',
                type: 'vocabulary',
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                title: 'AWL Sublist 3 Study Session',
                wordsLearned: 12,
                wordsMastered: 8,
                sublist: 3,
                timeSpent: 1800,
                points: 200
              }}
            />

            <ActivityCard
              activity={{
                id: 'demo-3',
                type: 'word_study',
                timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                title: 'Word Master: "analyze" family',
                wordsLearned: 6,
                timeSpent: 600,
                points: 75
              }}
              showDetails={false}
            />
          </div>
        </DashboardSection>

        {/* Integration Example */}
        <DashboardSection
          title="Combined Usage Example"
          subtitle="How components work together in a real dashboard section"
          icon={Trophy}
          action={{
            label: "View Full Dashboard",
            onClick: () => console.log('Navigate to dashboard')
          }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <StatCard
                label="Quiz Performance"
                value="92%"
                subtext="average score"
                icon={Trophy}
                iconColor="blue"
                motivationText="🌟 Excellent work!"
              />

              <div className="md:col-span-2">
                <ProgressBar
                  label="Monthly Learning Goal"
                  value={67}
                  max={100}
                  showPercentage
                  showValues
                  color="green"
                  size="lg"
                  subtitle="33 more activities to reach your monthly goal"
                />
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Recent Activities
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ActivityCard
                  activity={sampleActivity}
                  showDetails={false}
                />
                <ActivityCard
                  activity={{
                    id: 'demo-4',
                    type: 'vocabulary',
                    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
                    title: 'Quick Vocabulary Review',
                    wordsLearned: 5,
                    timeSpent: 300,
                    points: 50
                  }}
                  showDetails={false}
                />
              </div>
            </div>
          </div>
        </DashboardSection>
      </div>
    </div>
  );
};