import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Flame } from 'lucide-react';
import { StatCard } from '../StatCard';

describe('StatCard', () => {
  const defaultProps = {
    label: 'Test Metric',
    value: '42',
    icon: Flame
  };

  it('renders basic stat card correctly', () => {
    render(<StatCard {...defaultProps} />);
    
    expect(screen.getByText('Test Metric')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
  });

  it('displays subtext when provided', () => {
    render(
      <StatCard 
        {...defaultProps} 
        subtext="days in a row" 
      />
    );
    
    expect(screen.getByText('days in a row')).toBeInTheDocument();
  });

  it('displays motivation text when provided', () => {
    render(
      <StatCard 
        {...defaultProps} 
        motivationText="🔥 On fire!" 
      />
    );
    
    expect(screen.getByText('🔥 On fire!')).toBeInTheDocument();
  });

  it('displays upward trend correctly', () => {
    render(
      <StatCard 
        {...defaultProps} 
        trend={{
          direction: 'up',
          value: '+5%',
          label: 'this week'
        }}
      />
    );
    
    expect(screen.getByText('↗ +5% this week')).toBeInTheDocument();
  });

  it('displays downward trend correctly', () => {
    render(
      <StatCard 
        {...defaultProps} 
        trend={{
          direction: 'down',
          value: '-2%',
          label: 'this week'
        }}
      />
    );
    
    expect(screen.getByText('↘ -2% this week')).toBeInTheDocument();
  });

  it('handles click events when onClick is provided', () => {
    const handleClick = vi.fn();
    render(
      <StatCard 
        {...defaultProps} 
        onClick={handleClick}
      />
    );
    
    const card = screen.getByRole('button', { name: /test metric/i });
    fireEvent.click(card);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies correct icon color classes', () => {
    const { container } = render(
      <StatCard 
        {...defaultProps} 
        iconColor="green"
      />
    );
    
    const icon = container.querySelector('.green');
    expect(icon).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <StatCard 
        {...defaultProps} 
        className="custom-class"
      />
    );
    
    const card = container.querySelector('.custom-class');
    expect(card).toBeInTheDocument();
  });
});