import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ProgressBar } from '../ProgressBar';

describe('ProgressBar', () => {
  it('renders basic progress bar correctly', () => {
    render(<ProgressBar value={50} max={100} />);
    
    const progressBar = document.querySelector('[style*="width: 50%"]');
    expect(progressBar).toBeInTheDocument();
  });

  it('displays label when provided', () => {
    render(
      <ProgressBar 
        value={75} 
        max={100} 
        label="Weekly Goal" 
      />
    );
    
    expect(screen.getByText('Weekly Goal')).toBeInTheDocument();
  });

  it('shows percentage when showPercentage is true', () => {
    render(
      <ProgressBar 
        value={80} 
        max={100} 
        showPercentage={true}
      />
    );
    
    expect(screen.getByText('80%')).toBeInTheDocument();
  });

  it('shows values when showValues is true', () => {
    render(
      <ProgressBar 
        value={6} 
        max={10} 
        showValues={true}
      />
    );
    
    expect(screen.getByText('6/10')).toBeInTheDocument();
  });

  it('displays subtitle when provided', () => {
    render(
      <ProgressBar 
        value={90} 
        max={100} 
        subtitle="Almost there!"
      />
    );
    
    expect(screen.getByText('Almost there!')).toBeInTheDocument();
  });

  it('handles values over max correctly', () => {
    render(<ProgressBar value={120} max={100} />);
    
    const progressBar = document.querySelector('[style*="width: 100%"]');
    expect(progressBar).toBeInTheDocument();
  });

  it('handles negative values correctly', () => {
    render(<ProgressBar value={-10} max={100} />);
    
    const progressBar = document.querySelector('[style*="width: 0%"]');
    expect(progressBar).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { container } = render(
      <ProgressBar 
        value={50} 
        max={100} 
        size="lg"
      />
    );
    
    const progressBar = container.querySelector('.lg');
    expect(progressBar).toBeInTheDocument();
  });

  it('applies correct color classes', () => {
    const { container } = render(
      <ProgressBar 
        value={50} 
        max={100} 
        color="green"
      />
    );
    
    const progressFill = container.querySelector('.greenFill');
    expect(progressFill).toBeInTheDocument();
  });
});