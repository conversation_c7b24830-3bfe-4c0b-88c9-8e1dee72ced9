import React from 'react';
import { LucideIcon } from 'lucide-react';
import styles from './DashboardSection.module.css';

export interface DashboardSectionProps {
  /** Section title */
  title: string;
  
  /** Optional subtitle or description */
  subtitle?: string;
  
  /** Optional icon for the section */
  icon?: LucideIcon;
  
  /** Optional action button */
  action?: {
    label: string;
    onClick: () => void;
  };
  
  /** Content of the section */
  children: React.ReactNode;
  
  /** Custom CSS class */
  className?: string;
  
  /** Whether to add top margin (useful for stacking sections) */
  spacing?: 'none' | 'sm' | 'md' | 'lg';
}

/**
 * Reusable dashboard section component
 * Provides consistent layout and styling for dashboard sections
 */
export const DashboardSection: React.FC<DashboardSectionProps> = ({
  title,
  subtitle,
  icon: Icon,
  action,
  children,
  className = '',
  spacing = 'md'
}) => {
  const sectionClasses = `${styles.dashboardSection} ${styles[`spacing${spacing.charAt(0).toUpperCase() + spacing.slice(1)}`]} ${className}`;

  return (
    <section className={sectionClasses}>
      {/* Section Header */}
      <div className={styles.sectionHeader}>
        <div className={styles.sectionHeaderContent}>
          {Icon && <Icon className={styles.sectionIcon} />}
          <div className={styles.sectionTitleGroup}>
            <h3 className={styles.sectionTitle}>{title}</h3>
            {subtitle && (
              <p className={styles.sectionSubtitle}>{subtitle}</p>
            )}
          </div>
        </div>
        
        {action && (
          <button
            className={styles.sectionAction}
            onClick={action.onClick}
          >
            {action.label}
          </button>
        )}
      </div>

      {/* Section Content */}
      <div className={styles.sectionContent}>
        {children}
      </div>
    </section>
  );
};