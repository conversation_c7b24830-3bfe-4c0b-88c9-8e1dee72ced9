/* DashboardSection Component Styles */

.dashboardSection {
  @apply space-y-6;
}

/* Spacing variants */
.spacingNone {
  @apply mt-0;
}

.spacingSm {
  @apply mt-4;
}

.spacingMd {
  @apply mt-8;
}

.spacingLg {
  @apply mt-12;
}

.sectionHeader {
  @apply flex items-start justify-between;
}

.sectionHeaderContent {
  @apply flex items-start gap-3;
}

.sectionIcon {
  @apply w-6 h-6 text-gray-600 dark:text-gray-400 mt-0.5;
}

.sectionTitleGroup {
  @apply space-y-1;
}

.sectionTitle {
  @apply text-xl font-semibold text-gray-900 dark:text-white;
}

.sectionSubtitle {
  @apply text-sm text-gray-600 dark:text-gray-400 max-w-2xl;
}

.sectionAction {
  @apply px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors duration-200;
}

.sectionContent {
  @apply space-y-4;
}