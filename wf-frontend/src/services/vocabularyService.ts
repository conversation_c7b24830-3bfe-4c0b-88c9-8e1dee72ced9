/**
 * Vocabulary Service
 * Handles vocabulary-related business logic and orchestration
 * Follows Handler → Service → Repository pattern
 */

import { repositories } from '../repositories';
import { handleRepositoryCall, type ServiceResponse } from 'wf-shared/services';
import type { 
  WordDetails,
  WordSearchRequest,
  WordSearchResult,
  WordQuiz,
  VocabularyDashboard,
  AwlWord
} from '@/types';

export class VocabularyService {
  /**
   * Search for AWL words
   */
  static async searchWords(request: WordSearchRequest): Promise<ServiceResponse<WordSearchResult>> {
    return handleRepositoryCall(
      () => repositories.vocabularyApi.searchWords({
        query: request.query,
        mode: request.mode,
        includeWordForms: request.includeWordForms,
        limit: 10
      }),
      'Word search error',
      'Failed to search words'
    );
  }

  /**
   * Get detailed information about a specific word
   */
  static async getWordDetails(wordId: string): Promise<ServiceResponse<WordDetails>> {
    return handleRepositoryCall(
      () => repositories.vocabularyApi.getWordDetails(wordId),
      'Word details fetch error',
      'Failed to fetch word details'
    );
  }

  /**
   * Generate an interactive quiz for a specific word
   */
  static async generateWordQuiz(wordId: string): Promise<ServiceResponse<WordQuiz>> {
    return handleRepositoryCall(
      () => repositories.vocabularyApi.generateWordQuiz(wordId),
      'Quiz generation error',
      'Failed to generate quiz'
    );
  }

  /**
   * Save user's quiz attempt for a word
   */
  static async saveWordQuizAttempt(userId: string, wordId: string, attempt: Record<string, unknown>): Promise<ServiceResponse<{ success: boolean }>> {
    return handleRepositoryCall(
      () => repositories.vocabularyApi.saveWordQuizAttempt(userId, wordId, attempt),
      'Quiz attempt save error',
      'Failed to save quiz attempt'
    );
  }

  /**
   * Get user's word progress
   */
  static async getWordProgress(userId: string, wordId: string): Promise<ServiceResponse<Record<string, unknown> | null>> {
    return handleRepositoryCall(
      () => repositories.vocabularyApi.getWordProgress(userId, wordId),
      'Word progress fetch error',
      'Failed to fetch word progress'
    );
  }

  /**
   * Get user's vocabulary dashboard data
   */
  static async getDashboard(userId: string): Promise<ServiceResponse<VocabularyDashboard>> {
    return handleRepositoryCall(
      () => repositories.vocabularyApi.getDashboardData(userId),
      'Dashboard fetch error',
      'Failed to fetch dashboard data'
    ) as unknown as Promise<ServiceResponse<VocabularyDashboard>>;
  }

  /**
   * Get words by AWL sublist
   */
  static async getWordsBySublist(sublistNumber: number): Promise<ServiceResponse<AwlWord[]>> {
    return handleRepositoryCall(
      () => repositories.vocabularyApi.getWordsBySublist(sublistNumber),
      'Sublist words fetch error',
      'Failed to fetch sublist words'
    );
  }
}