import { repositories } from '../repositories';
import type { QuizQuestion, QuizResults, QuizAttempt, User } from '../types';
import { handleRepositoryCall, handleRepositoryCallSuccess, type ServiceResponse } from 'wf-shared/services';

/**
 * User Service
 * Handles all user-related operations including profile management, quiz attempts, and statistics
 */
export class UserService {
  /**
   * Save a quiz attempt with user answers
   */
  static async saveQuizAttempt(
    userId: string,
    quizId: string,
    results: QuizResults
  ): Promise<ServiceResponse<null>> {
    console.log('Saving quiz attempt:', { userId, quizId, results });
    
    return handleRepositoryCallSuccess(
      () => repositories.quizApi.saveQuizAttempt(userId, quizId, results),
      'Quiz attempt save error',
      'Failed to save quiz attempt'
    );
  }

  /**
   * Get user statistics across all attempts
   */
  static async getUserStats(
    userId: string
  ): Promise<ServiceResponse<Record<string, unknown>>> {
    return handleRepositoryCall(
      () => repositories.quizApi.getUserStats(userId),
      'User stats fetch error',
      'Failed to fetch user stats'
    );
  }

  /**
   * Get quiz attempt details with questions and user answers
   */
  static async getQuizAttemptDetails(attemptId: string): Promise<ServiceResponse<{
    questions: QuizQuestion[];
    userAnswers: Record<string, string>;
    attempt: QuizAttempt;
  }>> {
    return handleRepositoryCall(
      () => repositories.quizApi.getQuizAttemptDetails(attemptId),
      'Quiz attempt details fetch error',
      'Failed to fetch quiz attempt details'
    );
  }

  /**
   * Get user profile data from public.users table
   */
  static async getUserProfile(userId: string): Promise<ServiceResponse<User>> {
    return handleRepositoryCall(
      () => repositories.quizApi.getUserProfile(userId),
      'User profile fetch error',
      'Failed to fetch user profile'
    );
  }

  /**
   * Update user profile data in public.users table
   */
  static async updateUserProfile(
    userId: string,
    profileData: {
      first_name?: string;
      last_name?: string;
      level_id?: string;
    }
  ): Promise<ServiceResponse<User>> {
    return handleRepositoryCall(
      () => repositories.quizApi.updateUserProfile(userId, profileData),
      'User profile update error',
      'Failed to update user profile'
    );
  }

  /**
   * Get user quiz attempts with quiz details
   */
  static async getUserQuizAttempts(userId: string): Promise<ServiceResponse<QuizAttempt[]>> {
    return handleRepositoryCall(
      () => repositories.quizApi.getUserQuizAttempts(userId),
      'User quiz attempts fetch error',
      'Failed to fetch user quiz attempts'
    );
  }
}
