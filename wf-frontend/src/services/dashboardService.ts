/**
 * Dashboard Service
 * Unified service for combining quiz progress and vocabulary data
 * Follows Handler → Service → Repository pattern
 */

import { QuizService } from './quizService';
import { VocabularyService } from './vocabularyService';
import type { ServiceResponse } from 'wf-shared/services';
import type { 
  UnifiedDashboardData,
  CombinedLearningActivity,
  OverallStats,
  WeeklyDataPoint,
  Achievement
} from 'wf-shared/types';
import type { QuizAttempt, VocabularyDashboard, UserBadge } from '@/types';

export class DashboardService {
  /**
   * Get unified dashboard data combining quiz and vocabulary progress
   */
  static async getUnifiedDashboard(userId: string): Promise<ServiceResponse<UnifiedDashboardData>> {
    try {
      // For now, use mock data until quiz attempts service is properly implemented
      // TODO: Replace with actual quiz attempts when available
      const mockQuizAttempts: QuizAttempt[] = [];
      
      // Fetch vocabulary data
      const vocabularyDashboardResult = await VocabularyService.getDashboard(userId);
      
      if (vocabularyDashboardResult.error) {
        console.warn('Vocabulary dashboard fetch failed:', vocabularyDashboardResult.error);
      }

      const vocabularyData = vocabularyDashboardResult.data;

      // Combine data into unified format
      const unifiedData = this.combineDataSources(mockQuizAttempts, vocabularyData);

      return {
        data: unifiedData,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch dashboard data'
      };
    }
  }

  /**
   * Combine quiz and vocabulary data into unified dashboard format
   */
  private static combineDataSources(
    quizAttempts: QuizAttempt[], 
    vocabularyData: VocabularyDashboard | null
  ): UnifiedDashboardData {
    const quizStats = this.calculateQuizStats(quizAttempts);
    const vocabularyStats = vocabularyData?.stats || null;

    // Combine overall statistics
    const overallStats: OverallStats = {
      // Quiz-related stats
      totalQuizAttempts: quizStats.totalAttempts,
      averageQuizScore: quizStats.averageScore,
      bestQuizScore: quizStats.bestScore,
      totalQuizTimeSpent: quizStats.totalTimeSpent,

      // Vocabulary-related stats
      totalWordsStudied: vocabularyStats?.totalWordsStudied || 0,
      wordsMastered: vocabularyStats?.wordsMastered || 0,
      awlCoverage: vocabularyStats?.awlCoverage || 0,

      // Combined stats
      currentStreak: Math.max(
        this.calculateCurrentStreak(quizAttempts),
        vocabularyStats?.currentStreak || 0
      ),
      bestStreak: Math.max(
        this.calculateBestStreak(quizAttempts),
        vocabularyStats?.bestStreak || 0
      ),
      totalStudyTime: quizStats.totalTimeSpent + (vocabularyStats?.totalStudyTime || 0),
      totalPoints: (vocabularyStats?.totalPoints || 0) + this.calculateQuizPoints(quizAttempts),
      currentLevel: vocabularyStats?.currentLevel || 1,
    };

    // Generate weekly data
    const weeklyData = this.generateWeeklyData(quizAttempts, vocabularyData);

    // Get sublist progress
    const sublistProgress = vocabularyData?.sublistProgress || [];

    // Combine recent activities
    const recentActivity = this.combineRecentActivity(quizAttempts, vocabularyData);

    // Combine achievements
    const achievements = this.combineAchievements(vocabularyData, quizStats);

    return {
      overallStats,
      weeklyData,
      sublistProgress,
      recentActivity,
      achievements,
      weeklyGoal: 15, // Default weekly goal
      weeklyProgress: this.calculateWeeklyProgress(quizAttempts)
    };
  }

  /**
   * Calculate quiz-related statistics
   */
  private static calculateQuizStats(attempts: QuizAttempt[]) {
    if (attempts.length === 0) {
      return {
        totalAttempts: 0,
        averageScore: 0,
        bestScore: 0,
        totalTimeSpent: 0
      };
    }

    const totalAttempts = attempts.length;
    const averageScore = Math.round(
      attempts.reduce((sum, attempt) => sum + attempt.score, 0) / totalAttempts
    );
    const bestScore = Math.max(...attempts.map(attempt => attempt.score));
    const totalTimeSpent = attempts.reduce((sum, attempt) => sum + (attempt.time_taken_seconds || 0), 0);

    return {
      totalAttempts,
      averageScore,
      bestScore,
      totalTimeSpent
    };
  }

  /**
   * Calculate current learning streak
   */
  private static calculateCurrentStreak(attempts: QuizAttempt[]): number {
    if (attempts.length === 0) return 0;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let currentStreak = 0;
    let checkDate = new Date(today);

    // Check each day backwards from today
    for (let i = 0; i < 30; i++) { // Check last 30 days max
      const dayAttempts = attempts.filter(attempt => {
        const attemptDate = new Date(attempt.created_at);
        attemptDate.setHours(0, 0, 0, 0);
        return attemptDate.getTime() === checkDate.getTime();
      });

      if (dayAttempts.length > 0) {
        currentStreak++;
        checkDate.setDate(checkDate.getDate() - 1);
      } else {
        break;
      }
    }

    return currentStreak;
  }

  /**
   * Calculate best learning streak
   */
  private static calculateBestStreak(attempts: QuizAttempt[]): number {
    if (attempts.length === 0) return 0;

    // Group attempts by date
    const dailyAttempts = new Map<string, QuizAttempt[]>();
    attempts.forEach(attempt => {
      const date = new Date(attempt.created_at);
      date.setHours(0, 0, 0, 0);
      const dateKey = date.toISOString().split('T')[0];
      
      if (!dailyAttempts.has(dateKey)) {
        dailyAttempts.set(dateKey, []);
      }
      dailyAttempts.get(dateKey)!.push(attempt);
    });

    // Sort dates and find longest consecutive sequence
    const sortedDates = Array.from(dailyAttempts.keys()).sort();
    let bestStreak = 0;
    let currentStreak = 0;

    for (let i = 0; i < sortedDates.length; i++) {
      if (i === 0) {
        currentStreak = 1;
      } else {
        const prevDate = new Date(sortedDates[i - 1]);
        const currentDate = new Date(sortedDates[i]);
        const dayDiff = Math.round((currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24));
        
        if (dayDiff === 1) {
          currentStreak++;
        } else {
          bestStreak = Math.max(bestStreak, currentStreak);
          currentStreak = 1;
        }
      }
    }

    return Math.max(bestStreak, currentStreak);
  }

  /**
   * Calculate points from quiz attempts
   */
  private static calculateQuizPoints(attempts: QuizAttempt[]): number {
    return attempts.reduce((total, attempt) => {
      // Base points for completion
      let points = 10;
      
      // Bonus points based on score
      if (attempt.score >= 90) points += 15;
      else if (attempt.score >= 80) points += 10;
      else if (attempt.score >= 70) points += 5;
      
      // Bonus for perfect score
      if (attempt.score === 100) points += 5;
      
      return total + points;
    }, 0);
  }

  /**
   * Generate weekly data combining quiz and vocabulary activities
   */
  private static generateWeeklyData(
    quizAttempts: QuizAttempt[], 
    vocabularyData: VocabularyDashboard | null
  ): WeeklyDataPoint[] {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay() + 1); // Monday
    startOfWeek.setHours(0, 0, 0, 0);

    const weeklyData: WeeklyDataPoint[] = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      
      const dayName = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][i];
      const dateString = date.toISOString().split('T')[0];

      // Count quiz attempts for this day
      const dayQuizAttempts = quizAttempts.filter(attempt => {
        const attemptDate = new Date(attempt.created_at);
        return attemptDate.toDateString() === date.toDateString();
      });

      // Calculate metrics for this day
      const quizzes = dayQuizAttempts.length;
      const totalTimeSpent = dayQuizAttempts.reduce((sum, attempt) => 
        sum + (attempt.time_taken_seconds || 0), 0);
      
      // Estimate words studied (could be enhanced with vocabulary session data)
      const wordsStudied = Math.floor(quizzes * 8 + Math.random() * 5); // Rough estimate
      
      // Calculate points for the day
      const points = this.calculateQuizPoints(dayQuizAttempts);

      weeklyData.push({
        day: dayName,
        date: dateString,
        quizzes,
        wordsStudied,
        totalTimeSpent,
        points
      });
    }

    return weeklyData;
  }

  /**
   * Calculate weekly progress (quizzes this week)
   */
  private static calculateWeeklyProgress(attempts: QuizAttempt[]): number {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay() + 1); // Monday
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday
    endOfWeek.setHours(23, 59, 59, 999);

    return attempts.filter(attempt => {
      const attemptDate = new Date(attempt.created_at);
      return attemptDate >= startOfWeek && attemptDate <= endOfWeek;
    }).length;
  }

  /**
   * Combine recent activities from both quiz and vocabulary sources
   */
  private static combineRecentActivity(
    quizAttempts: QuizAttempt[], 
    vocabularyData: VocabularyDashboard | null
  ): CombinedLearningActivity[] {
    const activities: CombinedLearningActivity[] = [];

    // Add recent quiz attempts
    quizAttempts.slice(0, 10).forEach(attempt => {
      activities.push({
        id: `quiz-${attempt.id}`,
        type: 'quiz',
        timestamp: attempt.created_at,
        title: (attempt as any).quizzes?.title || 'Quiz Practice',
        score: attempt.score,
        correctAnswers: attempt.correct_answers,
        totalQuestions: attempt.total_questions,
        mode: attempt.mode,
        timeSpent: attempt.time_taken_seconds || 0,
        points: this.calculateQuizPoints([attempt])
      });
    });

    // Add vocabulary sessions (mock data for now)
    // TODO: Integrate with actual vocabulary session data
    if (vocabularyData?.stats) {
      activities.push({
        id: 'vocab-session-1',
        type: 'vocabulary',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        title: 'AWL Sublist 2 Study Session',
        wordsLearned: 8,
        wordsMastered: 3,
        sublist: 2,
        timeSpent: 900,
        points: 80
      });

      activities.push({
        id: 'word-study-1',
        type: 'word_study',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        title: 'Word Master: "analyze" word family',
        wordsLearned: 5,
        timeSpent: 600,
        points: 50
      });
    }

    // Sort by timestamp (most recent first)
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 15); // Limit to 15 most recent activities
  }

  /**
   * Combine achievements from vocabulary data and quiz performance
   */
  private static combineAchievements(
    vocabularyData: VocabularyDashboard | null,
    quizStats: { totalAttempts: number; averageScore: number; bestScore: number; totalTimeSpent: number }
  ): Achievement[] {
    const achievements: Achievement[] = [];

    // Add vocabulary achievements (using simplified structure for now)
    if (vocabularyData?.badges) {
      vocabularyData.badges.forEach(userBadge => {
        // Handle the actual UserBadge structure from the database
        const badgeData = userBadge.badge || userBadge;
        achievements.push({
          id: userBadge.id,
          name: badgeData.name || 'Achievement',
          description: badgeData.description || 'Earned achievement',
          category: 'vocabulary',
          earnedAt: userBadge.earned_at || new Date().toISOString(),
          criteria: {
            type: 'milestone',
            threshold: 1,
            description: badgeData.description || 'Achievement earned'
          },
          isActive: badgeData.is_active !== false
        });
      });
    }

    // Add quiz-based achievements
    if (quizStats.totalAttempts >= 10) {
      achievements.push({
        id: 'quiz-master-10',
        name: 'Quiz Master',
        description: 'Complete 10 quizzes',
        category: 'quiz',
        earnedAt: new Date().toISOString(),
        criteria: {
          type: 'count',
          threshold: 10,
          description: '10 quiz completions'
        },
        isActive: true
      });
    }

    if (quizStats.averageScore >= 85) {
      achievements.push({
        id: 'high-achiever',
        name: 'High Achiever',
        description: 'Maintain 85%+ average score',
        category: 'performance',
        earnedAt: new Date().toISOString(),
        criteria: {
          type: 'average_score',
          threshold: 85,
          description: '85%+ average score'
        },
        isActive: true
      });
    }

    return achievements;
  }
}