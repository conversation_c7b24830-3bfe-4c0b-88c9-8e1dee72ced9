import { describe, it, expect, vi, beforeEach } from 'vitest';
import { UserService } from '../userService';
import type { QuizAttempt, User } from '@/types';

// Mock the repositories
vi.mock('../../repositories', () => ({
  repositories: {
    quizApi: {
      saveQuizAttempt: vi.fn(),
      getUserStats: vi.fn(),
      getQuizAttemptDetails: vi.fn(),
      getUserProfile: vi.fn(),
      updateUserProfile: vi.fn(),
      getUserQuizAttempts: vi.fn()
    }
  }
}));

// Mock wf-shared/services
vi.mock('wf-shared/services', () => ({
  handleRepositoryCall: vi.fn(),
  handleRepositoryCallSuccess: vi.fn()
}));

import { handleRepositoryCall, handleRepositoryCallSuccess } from 'wf-shared/services';

const mockHandleRepositoryCall = handleRepositoryCall as ReturnType<typeof vi.fn>;
const mockHandleRepositoryCallSuccess = handleRepositoryCallSuccess as ReturnType<typeof vi.fn>;

describe('UserService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('saveQuizAttempt', () => {
    it('saves quiz attempt successfully', async () => {
      const mockResults = {
        correct: 8,
        total: 10,
        answered: 10,
        percentage: 80,
        timeSpent: 300,
        passed: true,
        mode: 'practice' as const,
        answers: []
      };

      mockHandleRepositoryCallSuccess.mockResolvedValue({
        data: null,
        error: null
      });

      const result = await UserService.saveQuizAttempt('user-1', 'quiz-1', mockResults);

      expect(mockHandleRepositoryCallSuccess).toHaveBeenCalledWith(
        expect.any(Function),
        'Quiz attempt save error',
        'Failed to save quiz attempt'
      );
      expect(result).toEqual({ data: null, error: null });
    });
  });

  describe('getUserStats', () => {
    it('fetches user stats successfully', async () => {
      const mockStats = {
        totalAttempts: 5,
        averageScore: 85,
        bestScore: 95,
        totalTimeSpent: 1200
      };

      mockHandleRepositoryCall.mockResolvedValue({
        data: mockStats,
        error: null
      });

      const result = await UserService.getUserStats('user-1');

      expect(mockHandleRepositoryCall).toHaveBeenCalledWith(
        expect.any(Function),
        'User stats fetch error',
        'Failed to fetch user stats'
      );
      expect(result.data).toEqual(mockStats);
      expect(result.error).toBeNull();
    });
  });

  describe('getQuizAttemptDetails', () => {
    it('fetches quiz attempt details successfully', async () => {
      const mockDetails = {
        questions: [],
        userAnswers: {},
        attempt: {
          id: 'attempt-1',
          user_id: 'user-1',
          quiz_id: 'quiz-1',
          mode: 'practice',
          score: 85,
          correct_answers: 8,
          total_questions: 10,
          time_taken_seconds: 300,
          completed_at: '2023-01-01T00:00:00Z',
          is_completed: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      mockHandleRepositoryCall.mockResolvedValue({
        data: mockDetails,
        error: null
      });

      const result = await UserService.getQuizAttemptDetails('attempt-1');

      expect(mockHandleRepositoryCall).toHaveBeenCalledWith(
        expect.any(Function),
        'Quiz attempt details fetch error',
        'Failed to fetch quiz attempt details'
      );
      expect(result.data).toEqual(mockDetails);
      expect(result.error).toBeNull();
    });
  });

  describe('getUserProfile', () => {
    it('fetches user profile successfully', async () => {
      const mockUser: User = {
        id: 'user-1',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        level_id: 'level-1',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      mockHandleRepositoryCall.mockResolvedValue({
        data: mockUser,
        error: null
      });

      const result = await UserService.getUserProfile('user-1');

      expect(mockHandleRepositoryCall).toHaveBeenCalledWith(
        expect.any(Function),
        'User profile fetch error',
        'Failed to fetch user profile'
      );
      expect(result.data).toEqual(mockUser);
      expect(result.error).toBeNull();
    });
  });

  describe('updateUserProfile', () => {
    it('updates user profile successfully', async () => {
      const mockUser: User = {
        id: 'user-1',
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        level_id: 'level-2',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };

      const profileData = {
        first_name: 'Jane',
        last_name: 'Smith',
        level_id: 'level-2'
      };

      mockHandleRepositoryCall.mockResolvedValue({
        data: mockUser,
        error: null
      });

      const result = await UserService.updateUserProfile('user-1', profileData);

      expect(mockHandleRepositoryCall).toHaveBeenCalledWith(
        expect.any(Function),
        'User profile update error',
        'Failed to update user profile'
      );
      expect(result.data).toEqual(mockUser);
      expect(result.error).toBeNull();
    });
  });

  describe('getUserQuizAttempts', () => {
    it('fetches user quiz attempts successfully', async () => {
      const mockAttempts: QuizAttempt[] = [
        {
          id: 'attempt-1',
          user_id: 'user-1',
          quiz_id: 'quiz-1',
          mode: 'practice',
          score: 85,
          correct_answers: 8,
          total_questions: 10,
          time_taken_seconds: 300,
          completed_at: '2023-01-01T00:00:00Z',
          is_completed: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ];

      mockHandleRepositoryCall.mockResolvedValue({
        data: mockAttempts,
        error: null
      });

      const result = await UserService.getUserQuizAttempts('user-1');

      expect(mockHandleRepositoryCall).toHaveBeenCalledWith(
        expect.any(Function),
        'User quiz attempts fetch error',
        'Failed to fetch user quiz attempts'
      );
      expect(result.data).toEqual(mockAttempts);
      expect(result.error).toBeNull();
    });
  });
});
