// Vocabulary API repository for frontend
// Handles vocabulary-related database operations for AWL words and definitions

import { BaseApiRepository } from 'wf-shared/repositories'
import type { ApiResponse } from 'wf-shared/types'
import type { 
  AwlWord, 
  WordDefinition, 
  VocabularyQuizTemplate,
  WordDetails,
  WordQuiz,
  WordSearchResult 
} from '@/types'

export class VocabularyApiRepository extends BaseApiRepository<AwlWord, Partial<AwlWord>, Partial<AwlWord>> {
  protected tableName = 'awl_words'

  constructor() {
    super()
  }

  protected applyFilters(_query: unknown, _filters: Partial<AwlWord>): unknown {
    // Implementation for filtering - placeholder for now
    return _query
  }

  /**
   * Search for AWL words with various modes
   */
  async searchWords(params: {
    query: string
    mode?: 'exact' | 'fuzzy' | 'contains'
    includeWordForms?: boolean
    limit?: number
  }): Promise<ApiResponse<WordSearchResult>> {
    try {
      const { query, mode = 'contains', includeWordForms = true, limit = 10 } = params

      if (!query.trim()) {
        return {
          success: false,
          error: { message: 'Search query cannot be empty', code: 'INVALID_QUERY' }
        }
      }

      const queryLower = query.toLowerCase().trim()

      // Execute query through circuit breaker
      console.log('Executing main search for:', queryLower);
      const result = await this.readCircuitBreaker.execute(async () => {
        // First try to find by main word
        let searchQuery = this.db
          .from(this.tableName)
          .select(`
            *,
            definitions:word_definitions(*),
            building_notes:word_building_notes(*),
            quiz_templates:vocabulary_quiz_templates(*)
          `)

        switch (mode) {
          case 'exact':
            searchQuery = searchQuery.eq('word', queryLower)
            break
          case 'fuzzy':
            searchQuery = searchQuery.ilike('word', `%${queryLower}%`)
            break
          case 'contains':
          default:
            searchQuery = searchQuery.ilike('word', `%${queryLower}%`)
            break
        }

        let mainResult = await searchQuery.limit(limit)
        
        // If no results and includeWordForms is true, search by word forms
        if (includeWordForms && (!mainResult.data || mainResult.data.length === 0)) {
          console.log('Searching by word forms for:', queryLower);
          
          // Get unique word IDs from word_definitions first
          const wordIdsResult = await this.db
            .from('word_definitions')
            .select('awl_word_id')
            .ilike('word_text', `%${queryLower}%`)
            .limit(limit)

          console.log('Word IDs result:', wordIdsResult);

          if (wordIdsResult.data && wordIdsResult.data.length > 0) {
            const uniqueWordIds = [...new Set(wordIdsResult.data.map(item => item.awl_word_id))]
            console.log('Unique word IDs:', uniqueWordIds);
            
            // Now get the full word data for these IDs
            const wordsResult = await this.db
              .from(this.tableName)
              .select(`
                *,
                definitions:word_definitions(*),
                building_notes:word_building_notes(*),
                quiz_templates:vocabulary_quiz_templates(*)
              `)
              .in('id', uniqueWordIds)
              .limit(limit)

            console.log('Words by IDs result:', wordsResult);
            mainResult = wordsResult
          }
        }

        return mainResult
      })

      if (!result.success) {
        console.log('Circuit breaker failed:', result.error);
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to search words', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      console.log('Circuit breaker success, result:', result);

      const { data: words, error } = result.data!

      if (error) {
        console.log('Database error:', error);
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      console.log('Found words after search:', words);

      // If exact match found, return word details
      if (words && words.length > 0) {
        const exactMatch = words.find(w => w.word.toLowerCase() === query.toLowerCase())
        const selectedWord = exactMatch || words[0]
        
        const wordDetails = await this.getWordDetails(selectedWord.id)
        
        if (!wordDetails.success) {
          return {
            success: false,
            error: wordDetails.error
          }
        }

        return {
          success: true,
          data: {
            found: true,
            word: wordDetails.data!,
            suggestions: words.slice(0, 5).map(w => w.word)
          }
        }
      }

      // If no matches, get suggestions
      const suggestions = await this.getSuggestions(query)
      
      return {
        success: true,
        data: {
          found: false,
          suggestions: suggestions.success ? suggestions.data || [] : []
        }
      }

    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Unknown error occurred', 
          code: 'NETWORK_ERROR' 
        }
      }
    }
  }

  /**
   * Get detailed information about a specific word
   */
  async getWordDetails(wordId: string): Promise<ApiResponse<WordDetails>> {
    try {
      // Get word with all related data through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            *,
            definitions:word_definitions(*),
            building_notes:word_building_notes(*),
            quiz_templates:vocabulary_quiz_templates(*)
          `)
          .eq('id', wordId)
          .single()
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch word details', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: word, error } = result.data!

      if (error || !word) {
        return {
          success: false,
          error: { message: 'Word not found', code: 'WORD_NOT_FOUND' }
        }
      }

      // Group definitions by word form
      const definitionsByForm: Record<string, WordDefinition[]> = {}
      if (word.definitions) {
        word.definitions.forEach((def: WordDefinition) => {
          if (!definitionsByForm[def.word_form]) {
            definitionsByForm[def.word_form] = []
          }
          definitionsByForm[def.word_form].push(def)
        })
      }

      // Generate quiz for this word
      const quiz = await this.generateWordQuiz(wordId)

      const wordDetails: WordDetails = {
        word: word as AwlWord,
        definitionsByForm,
        buildingNotes: word.building_notes || [],
        quiz: quiz.success ? quiz.data! : {
          wordId,
          questions: [],
          totalQuestions: 0,
          estimatedTime: 0
        },
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '1.0',
          completeness: this.calculateCompleteness(word)
        }
      }

      return {
        success: true,
        data: wordDetails
      }

    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to fetch word details', 
          code: 'DATABASE_ERROR' 
        }
      }
    }
  }

  /**
   * Generate an interactive quiz for a specific word
   */
  async generateWordQuiz(wordId: string): Promise<ApiResponse<WordQuiz>> {
    try {
      // Get quiz templates for this word through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from('vocabulary_quiz_templates')
          .select('*')
          .eq('awl_word_id', wordId)
          .limit(10)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch quiz templates', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: templates, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      // If no templates, generate default questions
      if (!templates || templates.length === 0) {
        return await this.generateDefaultQuiz(wordId)
      }

      // Convert templates to quiz questions
      const questions = templates.map((template: VocabularyQuizTemplate, index: number) => ({
        id: `q_${index + 1}`,
        questionText: template.question_template,
        correctAnswer: template.correct_answer,
        wordForm: template.target_word_form || 'base',
        difficulty: template.difficulty_level,
        hint: template.hint_text,
        context: template.context_clue
      }))

      const quiz: WordQuiz = {
        wordId,
        questions,
        totalQuestions: questions.length,
        estimatedTime: questions.length * 30 // 30 seconds per question
      }

      return {
        success: true,
        data: quiz
      }

    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to generate quiz', 
          code: 'QUIZ_GENERATION_FAILED' 
        }
      }
    }
  }

  /**
   * Get word suggestions for failed searches
   */
  private async getSuggestions(query: string): Promise<ApiResponse<string[]>> {
    try {
      // Get words that start with the query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select('word')
          .ilike('word', `${query}%`)
          .limit(5)
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch suggestions', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: suggestions, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: suggestions?.map(s => s.word) || []
      }

    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to get suggestions', 
          code: 'DATABASE_ERROR' 
        }
      }
    }
  }

  /**
   * Generate default quiz when no templates exist
   */
  private async generateDefaultQuiz(wordId: string): Promise<ApiResponse<WordQuiz>> {
    try {
      // Get word and definitions through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            *,
            definitions:word_definitions(*)
          `)
          .eq('id', wordId)
          .single()
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch word for quiz generation', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: word, error } = result.data!

      if (error || !word) {
        return {
          success: false,
          error: { message: 'Word not found for quiz generation', code: 'WORD_NOT_FOUND' }
        }
      }

      // Get distractors for multiple choice questions
      const distractors = await this.generateDistractors(word)

      const definitions = word.definitions || []
      const primaryDefinition = definitions.find(d => d.word_form === 'verb') || definitions[0]
      const questions = []

      // Question 1: Definition multiple choice
      if (primaryDefinition) {
        questions.push({
          id: 'q_1',
          questionText: `What does "${word.word}" mean?`,
          correctAnswer: primaryDefinition.english_definition,
          wordForm: primaryDefinition.word_form,
          difficulty: 1,
          hint: `This word is from AWL sublist ${word.sublists}`,
          context: primaryDefinition.example_sentences?.[0] || 'Academic context',
          questionType: 'multiple_choice',
          options: this.shuffleArray([
            primaryDefinition.english_definition,
            ...distractors.definitions.slice(0, 3)
          ])
        })
      }

      // Question 2: Fill in the blank with word forms
      const wordForms = this.extractWordForms(word.word_forms)
      const targetForm = wordForms.verb || wordForms.noun || word.word
      
      questions.push({
        id: 'q_2',
        questionText: `Complete the sentence: "The researchers need to _____ the data carefully before drawing conclusions."`,
        correctAnswer: targetForm,
        wordForm: 'fill_blank',
        difficulty: 2,
        hint: `Look for the ${wordForms.verb ? 'verb' : 'base'} form meaning "${primaryDefinition?.english_definition || 'to examine'}"`,
        context: 'Academic writing context',
        questionType: 'fill_blank'
      })

      // Question 3: Word form recognition
      if (definitions.length > 1) {
        const nounDefinition = definitions.find(d => d.word_form === 'noun')
        if (nounDefinition && wordForms.noun) {
          questions.push({
            id: 'q_3',
            questionText: `What is the noun form of "${word.word}"?`,
            correctAnswer: wordForms.noun,
            wordForm: 'noun',
            difficulty: 2,
            hint: 'Think about the word form that names the action or concept',
            context: 'Word formation',
            questionType: 'multiple_choice',
            options: this.shuffleArray([
              wordForms.noun,
              ...distractors.wordForms.slice(0, 3)
            ])
          })
        }
      }

      // Question 4: Context usage
      if (primaryDefinition?.example_sentences && primaryDefinition.example_sentences.length > 0) {
        const exampleSentence = primaryDefinition.example_sentences[0]
        const wordInSentence = this.findWordInSentence(exampleSentence, word.word, wordForms)
        
        if (wordInSentence) {
          const maskedSentence = exampleSentence.replace(new RegExp(`\\b${wordInSentence}\\b`, 'gi'), '_____')
          questions.push({
            id: 'q_4',
            questionText: `Complete the sentence: "${maskedSentence}"`,
            correctAnswer: wordInSentence,
            wordForm: 'context',
            difficulty: 3,
            hint: `Look for a word meaning "${primaryDefinition.english_definition}"`,
            context: 'Real usage example',
            questionType: 'fill_blank'
          })
        }
      }

      const quiz: WordQuiz = {
        wordId,
        questions,
        totalQuestions: questions.length,
        estimatedTime: questions.length * 45 // 45 seconds per question
      }

      return {
        success: true,
        data: quiz
      }

    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to generate default quiz', 
          code: 'QUIZ_GENERATION_FAILED' 
        }
      }
    }
  }

  /**
   * Generate distractor options for multiple choice questions
   */
  private async generateDistractors(word: Record<string, unknown>): Promise<{
    definitions: string[],
    wordForms: string[]
  }> {
    try {
      // Get similar words for distractors
      const distractorResult = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select(`
            word,
            word_forms,
            definitions:word_definitions(english_definition, word_form, word_text)
          `)
          .neq('id', word.id)
          .eq('sublists', word.sublists) // Same AWL sublist
          .limit(10)
      })

      const definitions: string[] = []
      const wordForms: string[] = []

      if (distractorResult.success && distractorResult.data?.data) {
        const distractorWords = distractorResult.data.data

        // Collect definition distractors
        distractorWords.forEach((w: Record<string, unknown>) => {
          if (Array.isArray(w.definitions)) {
            w.definitions.forEach((def: Record<string, unknown>) => {
              if (typeof def.english_definition === 'string' && def.english_definition.length < 100) {
                definitions.push(def.english_definition)
              }
            })
          }
        })

        // Collect word form distractors
        distractorWords.forEach((w: Record<string, unknown>) => {
          if (w.word_forms) {
            const forms = this.extractWordForms(w.word_forms)
            Object.values(forms).forEach(form => {
              if (typeof form === 'string') {
                wordForms.push(form)
              }
            })
          }
        })
      }

      // Add some generic academic distractors if not enough found
      const genericDefinitions = [
        'To make something smaller or reduce in size',
        'To increase or enhance the quality of something',
        'To remove or eliminate completely',
        'To create or establish something new',
        'To change or modify the structure',
        'To understand or comprehend fully'
      ]

      const genericWordForms = [
        'analysis', 'synthesis', 'evaluation', 'implementation',
        'development', 'assessment', 'investigation', 'interpretation'
      ]

      return {
        definitions: [...definitions, ...genericDefinitions].slice(0, 6),
        wordForms: [...wordForms, ...genericWordForms].slice(0, 6)
      }
    } catch {
      // Fallback distractors
      return {
        definitions: [
          'To make something smaller or reduce in size',
          'To increase or enhance the quality of something',
          'To remove or eliminate completely'
        ],
        wordForms: ['analysis', 'synthesis', 'evaluation']
      }
    }
  }

  /**
   * Extract word forms from word_forms field
   */
  private extractWordForms(wordForms: unknown): Record<string, string> {
    if (typeof wordForms === 'string') {
      try {
        return JSON.parse(wordForms)
      } catch {
        return {}
      }
    }
    if (typeof wordForms === 'object' && wordForms !== null) {
      return wordForms as Record<string, string>
    }
    return {}
  }

  /**
   * Find which form of the word appears in a sentence
   */
  private findWordInSentence(sentence: string, baseWord: string, wordForms: Record<string, string>): string | null {
    const allForms = [baseWord, ...Object.values(wordForms)]
    
    for (const form of allForms) {
      if (typeof form === 'string') {
        const regex = new RegExp(`\\b${form}\\b`, 'i')
        if (regex.test(sentence)) {
          return form
        }
      }
    }
    return null
  }

  /**
   * Shuffle array for randomizing multiple choice options
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  /**
   * Calculate completeness score for word data
   */
  private calculateCompleteness(word: Record<string, unknown>): number {
    let score = 0
    const maxScore = 100

    // Base word info (20 points)
    if (word.word) score += 10
    if (word.word_forms) score += 10

    // Definitions (40 points)
    if (Array.isArray(word.definitions) && word.definitions.length > 0) {
      score += 20
      if (word.definitions.some((d: Record<string, unknown>) => d.ipa_pronunciation)) score += 10
      if (word.definitions.some((d: Record<string, unknown>) => d.vietnamese_meaning)) score += 10
    }

    // Building notes and etymology (20 points)
    if (Array.isArray(word.building_notes) && word.building_notes.length > 0) score += 20

    // Quiz templates (20 points)
    if (Array.isArray(word.quiz_templates) && word.quiz_templates.length > 0) score += 20

    return Math.round((score / maxScore) * 100)
  }

  /**
   * Get user's vocabulary progress for a specific word
   */
  async getWordProgress(userId: string, wordId: string): Promise<ApiResponse<Record<string, unknown> | null>> {
    try {
      const { data, error } = await this.db
        .from('word_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('awl_word_id', wordId)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: data || null
      }
    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to fetch word progress', 
          code: 'DATABASE_ERROR' 
        }
      }
    }
  }

  /**
   * Save user's quiz attempt for a word
   */
  async saveWordQuizAttempt(userId: string, wordId: string, attempt: Record<string, unknown>): Promise<ApiResponse<{ success: boolean }>> {
    try {
      const score = Number(attempt.score) || 0;
      
      // First, check if progress record exists
      const { data: existingProgress } = await this.db
        .from('word_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('awl_word_id', wordId)
        .single()

      let progressData;
      
      if (existingProgress) {
        // Update existing record
        const currentAttempts = existingProgress.total_quiz_attempts || 0;
        const currentAverage = existingProgress.average_quiz_score || 0;
        const newAverage = currentAttempts > 0 
          ? ((currentAverage * currentAttempts) + score) / (currentAttempts + 1)
          : score;
          
        progressData = {
          times_studied: (existingProgress.times_studied || 0) + 1,
          total_quiz_attempts: currentAttempts + 1,
          best_quiz_score: Math.max(existingProgress.best_quiz_score || 0, score),
          average_quiz_score: newAverage,
          last_studied_at: new Date().toISOString(),
          study_status: score >= 80 ? 'mastered' : score >= 60 ? 'familiar' : 'learning',
          updated_at: new Date().toISOString()
        };
        
        const { error } = await this.db
          .from('word_progress')
          .update(progressData)
          .eq('user_id', userId)
          .eq('awl_word_id', wordId)
          
        if (error) {
          return {
            success: false,
            error: { message: error.message, code: error.code }
          }
        }
      } else {
        // Create new record
        progressData = {
          user_id: userId,
          awl_word_id: wordId,
          times_studied: 1,
          total_quiz_attempts: 1,
          best_quiz_score: score,
          average_quiz_score: score,
          last_studied_at: new Date().toISOString(),
          study_status: score >= 80 ? 'mastered' : score >= 60 ? 'familiar' : 'learning',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        const { error } = await this.db
          .from('word_progress')
          .insert(progressData)
          
        if (error) {
          return {
            success: false,
            error: { message: error.message, code: error.code }
          }
        }
      }

      return {
        success: true,
        data: { success: true }
      }
    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to save quiz attempt', 
          code: 'SAVE_ERROR' 
        }
      }
    }
  }

  /**
   * Get words by AWL sublist
   */
  async getWordsBySublist(sublistNumber: number): Promise<ApiResponse<AwlWord[]>> {
    try {
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select('*')
          .eq('sublists', sublistNumber)
          .eq('is_active', true)
          .order('frequency_rank', { ascending: true })
          .limit(100) // Reasonable limit per sublist
      })

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Circuit breaker: Failed to fetch sublist words', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: words, error } = result.data!

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: (words || []) as AwlWord[]
      }

    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to fetch sublist words', 
          code: 'DATABASE_ERROR' 
        }
      }
    }
  }

  /**
   * Get user's vocabulary dashboard data
   */
  async getDashboardData(_userId: string): Promise<ApiResponse<Record<string, unknown>>> {
    try {
      // TODO: Implement dashboard data fetching using unified progress system
      // This would query the user_dashboard view or aggregate from various tables
      
      return {
        success: true,
        data: {
          stats: {
            totalWordsStudied: 0,
            wordsMastered: 0,
            currentStreak: 0,
            bestStreak: 0,
            totalStudyTime: 0,
            averageQuizScore: 0,
            totalQuizAttempts: 0,
            totalPoints: 0,
            currentLevel: 1,
            awlCoverage: 0
          },
          sublistProgress: [],
          recentWords: [],
          badges: [],
          availableBadges: [],
          streaks: [],
          recentSessions: [],
          recommendations: []
        }
      }
    } catch (error) {
      return {
        success: false,
        error: { 
          message: error instanceof Error ? error.message : 'Failed to fetch dashboard', 
          code: 'DATABASE_ERROR' 
        }
      }
    }
  }
}