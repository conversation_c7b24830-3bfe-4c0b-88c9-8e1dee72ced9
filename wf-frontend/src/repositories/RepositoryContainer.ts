// Repository container for dependency injection and management
// Provides centralized access to all repositories in the frontend

// API Repositories
import { UserApiRepository } from './api/UserApiRepository'
import { QuizApiRepository } from './api/QuizApiRepository'
import { AuthApiRepository } from './api/AuthApiRepository'
import { VocabularyApiRepository } from './api/VocabularyApiRepository'

// Storage Repositories
import { AuthStorageRepository } from './storage/AuthStorageRepository'
import { UserStorageRepository } from './storage/UserStorageRepository'

export class RepositoryContainer {
  // API Repositories
  private _userApi?: UserApiRepository
  private _quizApi?: QuizApiRepository
  private _authApi?: AuthApiRepository
  private _vocabularyApi?: VocabularyApiRepository
  
  // Storage Repositories
  private _authStorage?: AuthStorageRepository
  private _userStorage?: UserStorageRepository

  constructor() {
    // No database injection needed - repositories use default client
  }

  // API Repository Getters (lazy initialization)
  get userApi(): UserApiRepository {
    if (!this._userApi) {
      this._userApi = new UserApiRepository()
    }
    return this._userApi
  }

  get quizApi(): QuizApiRepository {
    if (!this._quizApi) {
      this._quizApi = new QuizApiRepository()
    }
    return this._quizApi
  }

  get authApi(): AuthApiRepository {
    if (!this._authApi) {
      this._authApi = new AuthApiRepository()
    }
    return this._authApi
  }

  get vocabularyApi(): VocabularyApiRepository {
    if (!this._vocabularyApi) {
      this._vocabularyApi = new VocabularyApiRepository()
    }
    return this._vocabularyApi
  }

  // Storage Repository Getters (lazy initialization)
  get authStorage(): AuthStorageRepository {
    if (!this._authStorage) {
      this._authStorage = new AuthStorageRepository()
    }
    return this._authStorage
  }

  get userStorage(): UserStorageRepository {
    if (!this._userStorage) {
      this._userStorage = new UserStorageRepository()
    }
    return this._userStorage
  }

  /**
   * Reset all repositories (useful for testing or cleanup)
   */
  reset(): void {
    this._userApi = undefined
    this._quizApi = undefined
    this._authApi = undefined
    this._vocabularyApi = undefined
    this._authStorage = undefined
    this._userStorage = undefined
  }
}