import { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { DashboardService } from '@/services/dashboardService';
import type { 
  UnifiedDashboardData, 
  CombinedLearningActivity, 
  DashboardLoadingState 
} from 'wf-shared/types';
import type { DashboardTab } from '@/components/DashboardTabs';

/**
 * <PERSON><PERSON> hook for LearningDashboardScreen component
 * Manages unified dashboard state and business logic with real data integration
 */
export const useLearningDashboardScreen = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Tab state management with URL synchronization
  const getActiveTabFromUrl = (): DashboardTab => {
    const tab = searchParams.get('tab') as DashboardTab;
    return ['overview', 'performance', 'activity', 'improve'].includes(tab) ? tab : 'overview';
  };
  
  const [activeTab, setActiveTab] = useState<DashboardTab>(getActiveTabFromUrl());
  
  // State management
  const [dashboardData, setDashboardData] = useState<UnifiedDashboardData | null>(null);
  const [loading, setLoading] = useState<DashboardLoadingState>({
    overall: true,
    stats: true,
    weekly: true,
    sublists: true,
    activity: true,
    achievements: true,
  });
  const [error, setError] = useState<string | null>(null);

  // Real data loading using unified dashboard service
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user?.id) {
        setLoading({
          overall: false,
          stats: false,
          weekly: false,
          sublists: false,
          activity: false,
          achievements: false,
        });
        return;
      }

      try {
        setLoading((prev: DashboardLoadingState) => ({ ...prev, overall: true }));
        setError(null);
        
        // Use unified dashboard service to get combined data
        const result = await DashboardService.getUnifiedDashboard(user.id);
        
        if (result.error) {
          throw new Error(result.error);
        }
        
        setDashboardData(result.data);
        setLoading({
          overall: false,
          stats: false,
          weekly: false,
          sublists: false,
          activity: false,
          achievements: false,
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
        setLoading((prev: DashboardLoadingState) => ({ ...prev, overall: false }));
      }
    };

    loadDashboardData();
  }, [user?.id]);

  // Tab management handlers
  const handleTabChange = useCallback((tab: DashboardTab) => {
    setActiveTab(tab);
    setSearchParams(tab === 'overview' ? {} : { tab });
  }, [setSearchParams]);

  // Sync tab state with URL changes (e.g., browser back/forward)
  useEffect(() => {
    const urlTab = getActiveTabFromUrl();
    if (urlTab !== activeTab) {
      setActiveTab(urlTab);
    }
  }, [searchParams, activeTab]);

  // Computed properties
  const hasData = dashboardData && (
    dashboardData.overallStats.totalQuizAttempts > 0 || 
    dashboardData.overallStats.totalWordsStudied > 0
  );

  // Utility functions
  const formatTime = useCallback((seconds: number): string => {
    if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes}m`;
    }
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }, []);

  const formatDate = useCallback((dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInHours < 24) {
      if (diffInHours < 1) return 'Just now';
      return `${diffInHours}h ago`;
    }
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return date.toLocaleDateString();
  }, []);

  const getProgressPercentage = useCallback((): number => {
    if (!dashboardData) return 0;
    const totalAWLWords = 570; // Total AWL words
    const studiedWords = dashboardData.overallStats.totalWordsStudied;
    return Math.round((studiedWords / totalAWLWords) * 100);
  }, [dashboardData]);

  // Event handlers
  const handleTakeFirstQuiz = useCallback(() => {
    navigate('/practice');
  }, [navigate]);

  const handleViewActivity = useCallback((activity: CombinedLearningActivity) => {
    if (activity.type === 'quiz') {
      // Navigate to quiz results or retake quiz
      navigate('/practice');
    } else if (activity.type === 'vocabulary' || activity.type === 'word_study') {
      // Navigate to vocabulary tools
      navigate('/vocabulary-builder/word-master');
    }
  }, [navigate]);

  const handleStartTool = useCallback((toolId: string) => {
    // Track tool usage analytics
    console.log(`Starting tool: ${toolId}`);
    // Tool navigation is handled by Link component
  }, []);

  // Refresh data handler
  const refreshDashboard = useCallback(async () => {
    if (!user?.id) return;

    setLoading((prev: DashboardLoadingState) => ({ ...prev, overall: true }));
    try {
      const result = await DashboardService.getUnifiedDashboard(user.id);
      
      if (result.error) {
        throw new Error(result.error);
      }
      
      setDashboardData(result.data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh dashboard');
    } finally {
      setLoading((prev: DashboardLoadingState) => ({ ...prev, overall: false }));
    }
  }, [user?.id]);

  return {
    // Data
    dashboardData,
    loading,
    error,
    hasData,
    
    // Tab management
    activeTab,
    handleTabChange,
    
    // Computed values
    getProgressPercentage,
    
    // Utility functions
    formatTime,
    formatDate,
    
    // Event handlers
    handleTakeFirstQuiz,
    handleViewActivity,
    handleStartTool,
    refreshDashboard,
  };
};