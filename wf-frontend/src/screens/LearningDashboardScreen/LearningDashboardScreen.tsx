import React from 'react';
import { PageWrapper } from '@/components/Layout/ResponsiveContainer';
import { DashboardTabNavigation } from '@/components/DashboardTabs';
import { OverviewTab } from './tabs/OverviewTab';
import { PerformanceTab } from './tabs/PerformanceTab';
import { ActivityTab } from './tabs/ActivityTab';
import { ImproveTab } from './tabs/ImproveTab';
import { useLearningDashboardScreen } from './LearningDashboardScreen.handler';
import styles from './LearningDashboardScreen.module.css';

/**
 * LearningDashboardScreen component - Unified dashboard combining progress and vocabulary tracking
 * Pure UI component - all business logic handled by the handler
 */
const LearningDashboardScreen: React.FC = () => {
  const {
    dashboardData,
    loading,
    error,
    activeTab,
    handleTabChange,
    handleStartTool
  } = useLearningDashboardScreen();

  if (loading.overall) {
    return (
      <PageWrapper>
        <div className={styles.containerWrapper}>
          <div className={styles.loadingContainer}>
            <div className={styles.loadingContent}>
              <div className={styles.loadingSpinner}></div>
              <p className={styles.loadingText}>Loading your learning dashboard...</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  if (error) {
    return (
      <PageWrapper>
        <div className={styles.containerWrapper}>
          <div className="max-w-7xl mx-auto px-4 py-8">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6 text-center">
              <h2 className="text-lg font-semibold text-red-900 dark:text-red-300 mb-2">Unable to Load Dashboard</h2>
              <p className="text-red-700 dark:text-red-400">{error}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  const renderTabContent = () => {
    if (!dashboardData) return null;

    switch (activeTab) {
      case 'overview':
        return (
          <OverviewTab
            data={dashboardData}
            loading={loading.overall}
            onStartTool={handleStartTool}
          />
        );
      case 'performance':
        return (
          <PerformanceTab
            data={dashboardData}
            loading={loading.overall}
            onAnalyzeWeakAreas={() => console.log('Analyze weak areas')}
            onViewDetailedStats={() => console.log('View detailed stats')}
          />
        );
      case 'activity':
        return (
          <ActivityTab
            data={dashboardData}
            loading={loading.overall}
            onViewFullHistory={() => console.log('View full history')}
            onFilterByActivity={(type) => console.log('Filter by activity:', type)}
          />
        );
      case 'improve':
        return (
          <ImproveTab
            data={dashboardData}
            loading={loading.overall}
            onStartRecommendedQuiz={(quizId) => console.log('Start recommended quiz:', quizId)}
            onAccessTool={(toolId) => console.log('Access tool:', toolId)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <PageWrapper>
      <div className={styles.containerWrapper}>
        {/* Header */}
        <div className={styles.headerContainer}>
          <div className={styles.headerContent}>
            <h2 className={styles.title}>Learning Dashboard</h2>
            <p className={styles.subtitle}>
              Track your progress, continue your learning journey, and access your favorite tools
            </p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <DashboardTabNavigation
            activeTab={activeTab}
            onTabChange={handleTabChange}
          />

          {/* Tab Content */}
          <div className="min-h-96">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default LearningDashboardScreen;