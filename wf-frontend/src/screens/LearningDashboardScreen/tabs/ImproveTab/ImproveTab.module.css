/* Improve Tab Styles */

.improveTab {
  @apply space-y-8;
}

.tabDescription {
  @apply mb-6;
}

.tabDescriptionText {
  @apply text-gray-600 dark:text-gray-300 text-sm leading-relaxed;
}

.sectionTitle {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-6;
}

/* Loading States */
.loadingContainer {
  @apply flex flex-col items-center justify-center py-12;
}

.loadingSpinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.loadingText {
  @apply text-gray-600 dark:text-gray-300;
}

/* Recommendations Section */
.recommendationsSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.recommendationsGrid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.recommendationCard {
  @apply rounded-lg p-5 border-2 transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

.recommendationCard.blue {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700;
}

.recommendationCard.green {
  @apply bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700;
}

.recommendationCard.purple {
  @apply bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700;
}

.recommendationCard.orange {
  @apply bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-700;
}

.recommendationHeader {
  @apply flex items-center justify-between mb-3;
}

.recommendationIcon {
  @apply p-2 rounded-lg bg-white dark:bg-gray-700 shadow-sm;
}

.priorityBadge {
  @apply text-xs font-medium px-2 py-1 rounded-full;
}

.priorityBadge.high {
  @apply bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400;
}

.priorityBadge.medium {
  @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400;
}

.recommendationTitle {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-2;
}

.recommendationDescription {
  @apply text-sm text-gray-600 dark:text-gray-300 mb-4;
}

.recommendationAction {
  @apply flex items-center justify-center w-full px-4 py-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200;
}

/* Tools Section */
.toolsSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.toolsGrid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.toolCard {
  @apply block p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-xl border border-gray-200 dark:border-gray-600 hover:shadow-lg hover:-translate-y-1 transition-all duration-200;
  text-decoration: none;
}

.toolCard:hover {
  @apply border-blue-300 dark:border-blue-600;
}

.toolHeader {
  @apply flex items-center space-x-3 mb-3;
}

.toolIcon {
  @apply p-2 rounded-lg;
}

.toolIcon.blue {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400;
}

.toolIcon.green {
  @apply bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400;
}

.toolIcon.purple {
  @apply bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400;
}

.toolIcon.orange {
  @apply bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400;
}

.toolName {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.toolDescription {
  @apply text-sm text-gray-600 dark:text-gray-300 mb-4;
}

.toolFeatures {
  @apply flex flex-wrap gap-2 mb-4;
}

.featureTag {
  @apply text-xs bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-md border border-gray-200 dark:border-gray-700;
}

.toolAction {
  @apply flex items-center justify-between text-sm font-medium text-blue-600 dark:text-blue-400;
}

/* Tips Section */
.tipsSection {
  @apply bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-indigo-200 dark:border-indigo-700;
}

.tipsGrid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.tipCard {
  @apply flex items-start space-x-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-indigo-200 dark:border-indigo-700;
}

.tipIcon {
  @apply p-2 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-lg flex-shrink-0;
}

.tipContent {
  @apply flex-1;
}

.tipTitle {
  @apply font-semibold text-gray-900 dark:text-white mb-1;
}

.tipDescription {
  @apply text-sm text-gray-600 dark:text-gray-300;
}

/* Insights Section */
.insightsSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.insightsContainer {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.insightCard {
  @apply flex items-start space-x-4 p-5 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg border border-gray-200 dark:border-gray-600;
}

.insightIcon {
  @apply p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm flex-shrink-0;
}

.insightContent {
  @apply flex-1;
}

.insightTitle {
  @apply font-semibold text-gray-900 dark:text-white mb-2;
}

.insightText {
  @apply text-sm text-gray-600 dark:text-gray-300;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recommendationsGrid {
    @apply grid-cols-1;
  }
  
  .toolsGrid {
    @apply grid-cols-1;
  }
  
  .tipsGrid {
    @apply grid-cols-1;
  }
  
  .insightsContainer {
    @apply grid-cols-1;
  }
  
  .recommendationsSection,
  .toolsSection,
  .tipsSection,
  .insightsSection {
    @apply p-4;
  }
  
  .toolCard {
    @apply p-4;
  }
  
  .tipCard,
  .insightCard {
    @apply p-3;
  }
}

/* Animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recommendationCard,
.toolCard,
.tipCard,
.insightCard {
  animation: slideUp 0.3s ease-out;
}

/* Stagger animation delays */
.recommendationCard:nth-child(1) { animation-delay: 0.1s; }
.recommendationCard:nth-child(2) { animation-delay: 0.2s; }
.recommendationCard:nth-child(3) { animation-delay: 0.3s; }
.recommendationCard:nth-child(4) { animation-delay: 0.4s; }

.toolCard:nth-child(1) { animation-delay: 0.1s; }
.toolCard:nth-child(2) { animation-delay: 0.2s; }
.toolCard:nth-child(3) { animation-delay: 0.3s; }
.toolCard:nth-child(4) { animation-delay: 0.4s; }

.tipCard:nth-child(1) { animation-delay: 0.1s; }
.tipCard:nth-child(2) { animation-delay: 0.2s; }
.tipCard:nth-child(3) { animation-delay: 0.3s; }
.tipCard:nth-child(4) { animation-delay: 0.4s; }

.insightCard:nth-child(1) { animation-delay: 0.1s; }
.insightCard:nth-child(2) { animation-delay: 0.2s; }
.insightCard:nth-child(3) { animation-delay: 0.3s; }

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .recommendationCard,
  .toolCard,
  .tipCard,
  .insightCard {
    animation: none;
    transition: none;
  }
}