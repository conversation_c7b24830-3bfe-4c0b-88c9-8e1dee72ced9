import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Book<PERSON>pen, 
  FileText, 
  Target, 
  TrendingUp, 
  Brain, 
  Zap, 
  Settings,
  ArrowRight,
  Star
} from 'lucide-react';
import type { UnifiedDashboardData } from 'wf-shared/types';
import styles from './ImproveTab.module.css';

interface ImproveTabProps {
  data: UnifiedDashboardData;
  loading: boolean;
  onStartRecommendedQuiz: (quizId: string) => void;
  onAccessTool: (toolId: string) => void;
}

/**
 * ImproveTab component - Learning tools and personalized recommendations
 * Purpose: Help users discover tools and get recommendations for improvement
 * User Intent: "How can I improve? What tools can help me learn better?"
 */
export const ImproveTab: React.FC<ImproveTabProps> = ({
  data,
  loading,
  onStartRecommendedQuiz,
  onAccessTool
}) => {
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p className={styles.loadingText}>Loading improvement recommendations...</p>
      </div>
    );
  }

  const stats = data.overallStats;
  const weeklyData = data.weeklyData || [];

  // Generate personalized recommendations based on data
  const getPersonalizedRecommendations = () => {
    const recommendations = [];

    // Streak recommendations
    if (stats.currentStreak === 0) {
      recommendations.push({
        id: 'build-streak',
        title: 'Start Your Learning Streak',
        description: 'Take a quick quiz daily to build momentum and improve retention',
        priority: 'high',
        action: 'Take Practice Quiz',
        icon: <Target className="h-5 w-5" />,
        color: 'blue'
      });
    } else if (stats.currentStreak < 7) {
      recommendations.push({
        id: 'extend-streak',
        title: 'Extend Your Streak',
        description: `You're on a ${stats.currentStreak}-day streak! Keep it going to reach 7 days`,
        priority: 'medium',
        action: 'Continue Streak',
        icon: <Zap className="h-5 w-5" />,
        color: 'orange'
      });
    }

    // Performance recommendations
    if (stats.bestQuizScore < 80) {
      recommendations.push({
        id: 'improve-scores',
        title: 'Boost Your Quiz Scores',
        description: 'Focus on areas where you struggled to improve your performance',
        priority: 'high',
        action: 'Practice Weak Areas',
        icon: <TrendingUp className="h-5 w-5" />,
        color: 'green'
      });
    }

    // Vocabulary recommendations
    if (stats.totalWordsStudied < 100) {
      recommendations.push({
        id: 'expand-vocabulary',
        title: 'Expand Your Vocabulary',
        description: 'Explore more academic words to improve your language skills',
        priority: 'medium',
        action: 'Word Master',
        icon: <BookOpen className="h-5 w-5" />,
        color: 'purple'
      });
    }

    // Weekly goal recommendations
    if (weeklyData.length > 0) {
      const currentWeekProgress = data.weeklyProgress || 0;
      const weeklyGoal = data.weeklyGoal || 5;
      
      if (currentWeekProgress < weeklyGoal * 0.5) {
        recommendations.push({
          id: 'weekly-goal',
          title: 'Catch Up on Weekly Goal',
          description: `You're ${weeklyGoal - currentWeekProgress} activities away from your weekly goal`,
          priority: 'medium',
          action: 'Quick Practice',
          icon: <Target className="h-5 w-5" />,
          color: 'blue'
        });
      }
    }

    return recommendations.slice(0, 4); // Limit to 4 recommendations
  };

  const recommendations = getPersonalizedRecommendations();

  const learningTools = [
    {
      id: 'word-master',
      name: 'Word Master',
      description: 'Interactive vocabulary exploration with definitions and examples',
      icon: <BookOpen className="h-6 w-6" />,
      color: 'green',
      route: '/vocabulary-builder/word-master',
      features: ['Interactive learning', 'Progress tracking', 'Spaced repetition']
    },
    {
      id: 'awl-highlighter',
      name: 'AWL Highlighter',
      description: 'Analyze texts and identify academic vocabulary words',
      icon: <FileText className="h-6 w-6" />,
      color: 'blue',
      route: '/vocabulary-builder/awl-highlighter',
      features: ['Text analysis', 'Academic focus', 'Visual highlighting']
    },
    {
      id: 'practice-quizzes',
      name: 'Practice Quizzes',
      description: 'Test your knowledge with various quiz formats and difficulties',
      icon: <Brain className="h-6 w-6" />,
      color: 'purple',
      route: '/practice',
      features: ['Multiple formats', 'Instant feedback', 'Progress tracking']
    },
    {
      id: 'assessment-tests',
      name: 'Assessment Tests',
      description: 'Formal testing to measure your proficiency and progress',
      icon: <Settings className="h-6 w-6" />,
      color: 'orange',
      route: '/assessment',
      features: ['Formal testing', 'Detailed reports', 'Skill measurement']
    }
  ];

  const studyTips = [
    {
      title: 'Daily Consistency',
      description: 'Study for 15-20 minutes daily rather than long sessions once a week',
      icon: <Zap className="h-4 w-4" />
    },
    {
      title: 'Active Recall',
      description: 'Test yourself regularly instead of just re-reading material',
      icon: <Brain className="h-4 w-4" />
    },
    {
      title: 'Spaced Repetition',
      description: 'Review words at increasing intervals to improve long-term retention',
      icon: <TrendingUp className="h-4 w-4" />
    },
    {
      title: 'Contextual Learning',
      description: 'Learn words in sentences and real-world contexts, not isolation',
      icon: <BookOpen className="h-4 w-4" />
    }
  ];

  return (
    <div 
      className={styles.improveTab}
      role="tabpanel"
      id="tabpanel-improve"
      aria-labelledby="tab-improve"
    >
      {/* Tab Description */}
      <div className={styles.tabDescription}>
        <p className={styles.tabDescriptionText}>
          Personalized recommendations, learning tools, and resources to enhance your study experience
        </p>
      </div>

      {/* Personalized Recommendations */}
      {recommendations.length > 0 && (
        <div className={styles.recommendationsSection}>
          <h3 className={styles.sectionTitle}>Personalized Recommendations</h3>
          <div className={styles.recommendationsGrid}>
            {recommendations.map((rec) => (
              <div key={rec.id} className={`${styles.recommendationCard} ${styles[rec.color]}`}>
                <div className={styles.recommendationHeader}>
                  <div className={styles.recommendationIcon}>
                    {rec.icon}
                  </div>
                  <span className={`${styles.priorityBadge} ${styles[rec.priority]}`}>
                    {rec.priority === 'high' ? '🔥 High Priority' : '💡 Suggested'}
                  </span>
                </div>
                <h4 className={styles.recommendationTitle}>{rec.title}</h4>
                <p className={styles.recommendationDescription}>{rec.description}</p>
                <button 
                  className={styles.recommendationAction}
                  onClick={() => onStartRecommendedQuiz(rec.id)}
                >
                  {rec.action}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Learning Tools Hub */}
      <div className={styles.toolsSection}>
        <h3 className={styles.sectionTitle}>Learning Tools</h3>
        <div className={styles.toolsGrid}>
          {learningTools.map((tool) => (
            <Link
              key={tool.id}
              to={tool.route}
              className={styles.toolCard}
              onClick={() => onAccessTool(tool.id)}
            >
              <div className={styles.toolHeader}>
                <div className={`${styles.toolIcon} ${styles[tool.color]}`}>
                  {tool.icon}
                </div>
                <h4 className={styles.toolName}>{tool.name}</h4>
              </div>
              <p className={styles.toolDescription}>{tool.description}</p>
              <div className={styles.toolFeatures}>
                {tool.features.map((feature, index) => (
                  <span key={index} className={styles.featureTag}>
                    {feature}
                  </span>
                ))}
              </div>
              <div className={styles.toolAction}>
                <span>Open Tool</span>
                <ArrowRight className="h-4 w-4" />
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Study Tips */}
      <div className={styles.tipsSection}>
        <h3 className={styles.sectionTitle}>Study Tips & Best Practices</h3>
        <div className={styles.tipsGrid}>
          {studyTips.map((tip, index) => (
            <div key={index} className={styles.tipCard}>
              <div className={styles.tipIcon}>
                {tip.icon}
              </div>
              <div className={styles.tipContent}>
                <h4 className={styles.tipTitle}>{tip.title}</h4>
                <p className={styles.tipDescription}>{tip.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Progress Insights */}
      <div className={styles.insightsSection}>
        <h3 className={styles.sectionTitle}>Your Progress Insights</h3>
        <div className={styles.insightsContainer}>
          <div className={styles.insightCard}>
            <div className={styles.insightIcon}>
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
            <div className={styles.insightContent}>
              <h4 className={styles.insightTitle}>Strengths</h4>
              <p className={styles.insightText}>
                {stats.bestQuizScore >= 85 ? 'Excellent quiz performance' :
                 stats.currentStreak >= 5 ? 'Great consistency in learning' :
                 stats.totalWordsStudied >= 50 ? 'Good vocabulary exploration' :
                 'Building foundational knowledge'}
              </p>
            </div>
          </div>

          <div className={styles.insightCard}>
            <div className={styles.insightIcon}>
              <Target className="h-6 w-6 text-blue-600" />
            </div>
            <div className={styles.insightContent}>
              <h4 className={styles.insightTitle}>Focus Areas</h4>
              <p className={styles.insightText}>
                {stats.bestQuizScore < 70 ? 'Improve quiz performance with more practice' :
                 stats.currentStreak === 0 ? 'Build a consistent daily study habit' :
                 stats.totalWordsStudied < 30 ? 'Expand vocabulary knowledge' :
                 'Continue current learning path'}
              </p>
            </div>
          </div>

          <div className={styles.insightCard}>
            <div className={styles.insightIcon}>
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
            <div className={styles.insightContent}>
              <h4 className={styles.insightTitle}>Next Steps</h4>
              <p className={styles.insightText}>
                {recommendations.length > 0 ? 
                  `Follow the ${recommendations[0].title.toLowerCase()} recommendation above` :
                  'Continue your excellent learning momentum'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};