import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Book<PERSON>pen, 
  FileText, 
  Lightbulb, 
  Flame, 
  Target, 
  CheckCircle, 
  Star,
  Trophy
} from 'lucide-react';
import type { UnifiedDashboardData } from 'wf-shared/types';
import { StatCard, DashboardSection, ProgressBar, ActivityCard } from '@/components/Dashboard';
import styles from './OverviewTab.module.css';

interface OverviewTabProps {
  data: UnifiedDashboardData;
  loading: boolean;
  onStartTool: (toolId: string) => void;
}

/**
 * OverviewTab component - Default dashboard tab showing key stats and quick actions
 * Purpose: Quick snapshot + immediate actions
 * User Intent: "How am I doing overall? What should I do next?"
 */
export const OverviewTab: React.FC<OverviewTabProps> = ({
  data,
  loading,
  onStartTool
}) => {
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p className={styles.loadingText}>Loading your overview...</p>
      </div>
    );
  }

  const stats = data.overallStats;
  const weeklyGoal = data.weeklyGoal;
  const weeklyProgress = data.weeklyProgress;
  const latestAchievement = data.achievements[0]; // Most recent achievement

  const goalProgressPercentage = Math.min((weeklyProgress / weeklyGoal) * 100, 100);
  const activitiesRemaining = Math.max(weeklyGoal - weeklyProgress, 0);

  return (
    <div 
      className={styles.overviewTab}
      role="tabpanel"
      id="tabpanel-overview"
      aria-labelledby="tab-overview"
    >
      {/* Tab Description */}
      <div className={styles.tabDescription}>
        <p className={styles.tabDescriptionText}>
          Your learning snapshot with key stats and quick actions to continue your journey
        </p>
      </div>

      <div className={styles.overviewGrid}>
        {/* Key Stats Cards Section */}
        <div className={styles.statsSection}>
          <h3 className={styles.sectionTitle}>Your Learning Snapshot</h3>
          <div className={styles.statsGrid}>
            {/* Current Streak Card */}
            <div className={styles.statCard}>
              <div className={styles.statCardContent}>
                <div className={styles.statCardHeader}>
                  <Flame className={`${styles.statIcon} ${styles.orange}`} />
                  <span className={styles.statLabel}>Current Streak</span>
                </div>
                <div className={styles.statValue}>{stats.currentStreak}</div>
                <div className={styles.statSubtext}>
                  {stats.currentStreak === 1 ? 'day in a row' : 'days in a row'}
                </div>
                {stats.currentStreak > 0 && (
                  <div className={styles.motivationText}>
                    {stats.currentStreak >= 7 ? '🔥 On fire!' : 'Keep it up!'}
                  </div>
                )}
              </div>
            </div>

            {/* Weekly Progress Card */}
            <div className={styles.statCard}>
              <div className={styles.statCardContent}>
                <div className={styles.statCardHeader}>
                  <Target className={`${styles.statIcon} ${styles.blue}`} />
                  <span className={styles.statLabel}>Weekly Progress</span>
                </div>
                <div className={styles.statValue}>{weeklyProgress}/{weeklyGoal}</div>
                <div className={styles.statSubtext}>activities completed</div>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progressFill}
                    style={{ width: `${goalProgressPercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Best Quiz Score Card */}
            <div className={styles.statCard}>
              <div className={styles.statCardContent}>
                <div className={styles.statCardHeader}>
                  <CheckCircle className={`${styles.statIcon} ${styles.green}`} />
                  <span className={styles.statLabel}>Best Score</span>
                </div>
                <div className={styles.statValue}>{stats.bestQuizScore}%</div>
                <div className={styles.statSubtext}>personal best</div>
                <div className={styles.motivationText}>
                  {stats.bestQuizScore >= 90 ? '🌟 Excellent!' : 
                   stats.bestQuizScore >= 80 ? '⭐ Great work!' : 
                   'Keep improving!'}
                </div>
              </div>
            </div>

            {/* Words Mastered Card */}
            <div className={styles.statCard}>
              <div className={styles.statCardContent}>
                <div className={styles.statCardHeader}>
                  <BookOpen className={`${styles.statIcon} ${styles.purple}`} />
                  <span className={styles.statLabel}>Words Mastered</span>
                </div>
                <div className={styles.statValue}>{stats.wordsMastered}</div>
                <div className={styles.statSubtext}>
                  of {stats.totalWordsStudied} studied
                </div>
                <div className={styles.motivationText}>
                  {((stats.wordsMastered / stats.totalWordsStudied) * 100).toFixed(0)}% mastery rate
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Section */}
        <div className={styles.actionsSection}>
          <h3 className={styles.sectionTitle}>Quick Actions</h3>
          <div className={styles.actionsList}>
            <Link
              to="/vocabulary-builder/word-master"
              className={styles.actionButton}
              onClick={() => onStartTool('word-master')}
            >
              <BookOpen className={`${styles.actionIcon} ${styles.green}`} />
              <div className={styles.actionContent}>
                <div className={styles.actionTitle}>Word Master</div>
                <div className={styles.actionDescription}>
                  {stats.totalWordsStudied > 0 ? 'Continue studying' : 'Start exploring words'}
                </div>
              </div>
              <div className={styles.actionArrow}>→</div>
            </Link>

            <Link
              to="/vocabulary-builder/awl-highlighter"
              className={styles.actionButton}
              onClick={() => onStartTool('awl-highlighter')}
            >
              <FileText className={`${styles.actionIcon} ${styles.blue}`} />
              <div className={styles.actionContent}>
                <div className={styles.actionTitle}>AWL Highlighter</div>
                <div className={styles.actionDescription}>
                  Analyze text for academic words
                </div>
              </div>
              <div className={styles.actionArrow}>→</div>
            </Link>

            <Link
              to="/practice"
              className={styles.actionButton}
              onClick={() => onStartTool('practice')}
            >
              <Lightbulb className={`${styles.actionIcon} ${styles.purple}`} />
              <div className={styles.actionContent}>
                <div className={styles.actionTitle}>Practice Quizzes</div>
                <div className={styles.actionDescription}>
                  Test your knowledge
                </div>
              </div>
              <div className={styles.actionArrow}>→</div>
            </Link>
          </div>
        </div>
      </div>

      {/* Weekly Goal Progress Section */}
      <div className={styles.goalSection}>
        <div className={styles.goalHeader}>
          <h3 className={styles.sectionTitle}>Weekly Learning Goal</h3>
          <div className={styles.goalProgress}>
            {goalProgressPercentage >= 100 ? (
              <span className={styles.goalAchieved}>🎉 Goal Achieved!</span>
            ) : (
              <span className={styles.goalRemaining}>
                {activitiesRemaining} more {activitiesRemaining === 1 ? 'activity' : 'activities'} to go
              </span>
            )}
          </div>
        </div>
        
        <div className={styles.goalBar}>
          <div 
            className={styles.goalBarFill}
            style={{ width: `${goalProgressPercentage}%` }}
          ></div>
        </div>
        
        <div className={styles.goalStats}>
          <span className={styles.goalCurrent}>{weeklyProgress} completed</span>
          <span className={styles.goalTarget}>Goal: {weeklyGoal}</span>
        </div>

        <div className={styles.motivationalMessage}>
          {goalProgressPercentage >= 100 ? (
            "Outstanding! You've exceeded your weekly goal. Keep up the excellent work! 🌟"
          ) : goalProgressPercentage >= 80 ? (
            "You're almost there! Just a few more activities to reach your goal! 💪"
          ) : goalProgressPercentage >= 50 ? (
            "Great progress! You're halfway to your weekly goal. Keep going! 🚀"
          ) : goalProgressPercentage > 0 ? (
            "Good start! Every step counts towards your learning journey. 📚"
          ) : (
            "Ready to start your learning week? Choose an activity above to begin! ✨"
          )}
        </div>
      </div>

      {/* Latest Achievement Section */}
      {latestAchievement && (
        <div className={styles.achievementSection}>
          <h3 className={styles.sectionTitle}>Latest Achievement</h3>
          <div className={styles.achievementCard}>
            <div className={styles.achievementIcon}>
              <Trophy className="h-6 w-6 text-yellow-600" />
            </div>
            <div className={styles.achievementContent}>
              <div className={styles.achievementName}>{latestAchievement.name}</div>
              <div className={styles.achievementDescription}>{latestAchievement.description}</div>
              <div className={styles.achievementDate}>
                Earned {new Date(latestAchievement.earnedAt).toLocaleDateString()}
              </div>
            </div>
            <div className={styles.achievementBadge}>
              <Star className="h-4 w-4 text-yellow-600" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};