/* Performance Tab Styles */

.performanceTab {
  @apply space-y-8;
}

.tabDescription {
  @apply mb-6;
}

.tabDescriptionText {
  @apply text-gray-600 dark:text-gray-300 text-sm leading-relaxed;
}

.sectionTitle {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-6;
}

/* Loading States */
.loadingContainer {
  @apply flex flex-col items-center justify-center py-12;
}

.loadingSpinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.loadingText {
  @apply text-gray-600 dark:text-gray-300;
}

/* Performance Overview Section */
.overviewSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.metricsGrid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.metricCard {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-5 border border-gray-100 dark:border-gray-600 hover:shadow-md transition-all duration-200;
}

.metricHeader {
  @apply flex items-center space-x-3 mb-3;
}

.metricIcon {
  @apply h-5 w-5;
}

.metricIcon.blue {
  @apply text-blue-600;
}

.metricIcon.green {
  @apply text-green-600;
}

.metricIcon.purple {
  @apply text-purple-600;
}

.metricIcon.orange {
  @apply text-orange-600;
}

.metricLabel {
  @apply text-sm font-medium text-gray-600 dark:text-gray-300;
}

.metricValue {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-2;
}

.metricSubtext {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.metricTrend {
  @apply text-sm font-medium;
}

.trendPositive {
  @apply text-green-600 dark:text-green-400;
}

.trendNegative {
  @apply text-red-600 dark:text-red-400;
}

.trendNeutral {
  @apply text-gray-600 dark:text-gray-400;
}

/* Chart Section */
.chartSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.chartContainer {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-6;
}

.progressChart {
  @apply flex items-end justify-center space-x-4 h-64;
}

.chartBar {
  @apply flex flex-col items-center space-y-2 flex-1 max-w-16;
}

.barFill {
  @apply w-full rounded-t-md transition-all duration-1000 ease-out min-h-2;
}

.barLabel {
  @apply text-xs font-medium text-gray-600 dark:text-gray-300;
}

.barValue {
  @apply text-xs font-bold text-gray-900 dark:text-white;
}

.emptyChart {
  @apply flex flex-col items-center justify-center py-12;
}

.emptyIcon {
  @apply h-12 w-12 text-gray-400 dark:text-gray-500 mb-4;
}

.emptyText {
  @apply text-gray-600 dark:text-gray-300 text-center;
}

/* Vocabulary Section */
.vocabularySection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.sublistGrid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.sublistCard {
  @apply bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700;
}

.sublistHeader {
  @apply flex items-center justify-between mb-3;
}

.sublistName {
  @apply font-semibold text-gray-900 dark:text-white text-sm;
}

.sublistPercentage {
  @apply font-bold text-purple-600 dark:text-purple-400 text-sm;
}

.sublistProgress {
  @apply w-full bg-white dark:bg-gray-700 rounded-full h-2 mb-3;
}

.sublistBar {
  @apply bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-1000;
}

.sublistStats {
  @apply flex justify-between text-xs text-gray-600 dark:text-gray-300;
}

.wordsLearned {
  @apply font-medium;
}

.totalWords {
  @apply font-normal;
}

.emptyVocabulary {
  @apply col-span-full flex flex-col items-center justify-center py-12;
}

/* Action Section */
.actionSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.actionButtons {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.actionButton {
  @apply flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-200 hover:shadow-md;
}

.actionButton:hover {
  @apply transform -translate-y-1;
}

.actionIcon {
  @apply h-6 w-6 text-blue-600 dark:text-blue-400 mr-4 flex-shrink-0;
}

.actionContent {
  @apply flex flex-col;
}

.actionTitle {
  @apply font-semibold text-gray-900 dark:text-white text-sm;
}

.actionDescription {
  @apply text-sm text-gray-600 dark:text-gray-300;
}

/* Responsive Design */
@media (max-width: 768px) {
  .metricsGrid {
    @apply grid-cols-1 gap-4;
  }
  
  .sublistGrid {
    @apply grid-cols-1 gap-4;
  }
  
  .progressChart {
    @apply space-x-2 h-48;
  }
  
  .actionButtons {
    @apply grid-cols-1;
  }
  
  .overviewSection,
  .chartSection,
  .vocabularySection,
  .actionSection {
    @apply p-4;
  }
}

/* Animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metricCard,
.sublistCard,
.actionButton {
  animation: slideUp 0.3s ease-out;
}

/* Stagger animation delays */
.metricCard:nth-child(1) { animation-delay: 0.1s; }
.metricCard:nth-child(2) { animation-delay: 0.2s; }
.metricCard:nth-child(3) { animation-delay: 0.3s; }
.metricCard:nth-child(4) { animation-delay: 0.4s; }

.sublistCard:nth-child(1) { animation-delay: 0.1s; }
.sublistCard:nth-child(2) { animation-delay: 0.2s; }
.sublistCard:nth-child(3) { animation-delay: 0.3s; }
.sublistCard:nth-child(4) { animation-delay: 0.4s; }
.sublistCard:nth-child(5) { animation-delay: 0.5s; }
.sublistCard:nth-child(6) { animation-delay: 0.6s; }

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .metricCard,
  .sublistCard,
  .actionButton,
  .barFill,
  .sublistBar {
    animation: none;
    transition: none;
  }
}