import React from 'react';
import { TrendingUp, Target, BarChart3, Award, BookO<PERSON>, Brain } from 'lucide-react';
import type { UnifiedDashboardData } from 'wf-shared/types';
import styles from './PerformanceTab.module.css';

interface PerformanceTabProps {
  data: UnifiedDashboardData;
  loading: boolean;
  onAnalyzeWeakAreas: () => void;
  onViewDetailedStats: () => void;
}

/**
 * PerformanceTab component - Analytics and progress tracking
 * Purpose: Detailed performance insights and improvement tracking
 * User Intent: "How am I improving? Where are my strengths and weaknesses?"
 */
export const PerformanceTab: React.FC<PerformanceTabProps> = ({
  data,
  loading,
  onAnalyzeWeakAreas,
  onViewDetailedStats
}) => {
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p className={styles.loadingText}>Loading performance analytics...</p>
      </div>
    );
  }

  const stats = data.overallStats;
  const weeklyData = data.weeklyData || [];
  const sublistProgress = data.sublistProgress || [];

  // Calculate performance metrics
  const improvementTrend = weeklyData.length >= 2 
    ? weeklyData[weeklyData.length - 1].quizScore - weeklyData[0].quizScore 
    : 0;
  
  const averageScore = weeklyData.length > 0 
    ? weeklyData.reduce((sum, week) => sum + week.quizScore, 0) / weeklyData.length 
    : 0;

  const consistencyScore = weeklyData.length > 0 
    ? Math.max(0, 100 - (Math.max(...weeklyData.map(w => w.quizScore)) - Math.min(...weeklyData.map(w => w.quizScore))))
    : 0;

  const vocabularyMastery = sublistProgress.length > 0
    ? sublistProgress.reduce((sum, sublist) => sum + sublist.masteryPercentage, 0) / sublistProgress.length
    : 0;

  return (
    <div 
      className={styles.performanceTab}
      role="tabpanel"
      id="tabpanel-performance"
      aria-labelledby="tab-performance"
    >
      {/* Tab Description */}
      <div className={styles.tabDescription}>
        <p className={styles.tabDescriptionText}>
          Detailed analytics on your quiz scores, improvement trends, and vocabulary mastery progress
        </p>
      </div>

      {/* Performance Overview Cards */}
      <div className={styles.overviewSection}>
        <h3 className={styles.sectionTitle}>Performance Overview</h3>
        <div className={styles.metricsGrid}>
          {/* Average Score Card */}
          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <Target className={`${styles.metricIcon} ${styles.blue}`} />
              <span className={styles.metricLabel}>Average Score</span>
            </div>
            <div className={styles.metricValue}>{averageScore.toFixed(1)}%</div>
            <div className={styles.metricTrend}>
              {improvementTrend > 0 && (
                <span className={styles.trendPositive}>
                  ↗ +{improvementTrend.toFixed(1)}% this week
                </span>
              )}
              {improvementTrend < 0 && (
                <span className={styles.trendNegative}>
                  ↘ {improvementTrend.toFixed(1)}% this week
                </span>
              )}
              {improvementTrend === 0 && (
                <span className={styles.trendNeutral}>
                  → Stable performance
                </span>
              )}
            </div>
          </div>

          {/* Consistency Score Card */}
          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <BarChart3 className={`${styles.metricIcon} ${styles.green}`} />
              <span className={styles.metricLabel}>Consistency</span>
            </div>
            <div className={styles.metricValue}>{consistencyScore.toFixed(0)}%</div>
            <div className={styles.metricSubtext}>
              {consistencyScore >= 80 ? 'Very consistent' : 
               consistencyScore >= 60 ? 'Moderately consistent' : 
               'Room for improvement'}
            </div>
          </div>

          {/* Vocabulary Mastery Card */}
          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <BookOpen className={`${styles.metricIcon} ${styles.purple}`} />
              <span className={styles.metricLabel}>Vocabulary Mastery</span>
            </div>
            <div className={styles.metricValue}>{vocabularyMastery.toFixed(0)}%</div>
            <div className={styles.metricSubtext}>
              {stats.wordsMastered} of {stats.totalWordsStudied} words mastered
            </div>
          </div>

          {/* Best Performance Card */}
          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <Award className={`${styles.metricIcon} ${styles.orange}`} />
              <span className={styles.metricLabel}>Personal Best</span>
            </div>
            <div className={styles.metricValue}>{stats.bestQuizScore}%</div>
            <div className={styles.metricSubtext}>
              {stats.bestQuizScore >= 90 ? '🌟 Outstanding!' : 
               stats.bestQuizScore >= 80 ? '🎯 Excellent!' : 
               'Keep pushing forward!'}
            </div>
          </div>
        </div>
      </div>

      {/* Weekly Progress Chart */}
      <div className={styles.chartSection}>
        <h3 className={styles.sectionTitle}>Weekly Progress Trend</h3>
        <div className={styles.chartContainer}>
          {weeklyData.length > 0 ? (
            <div className={styles.progressChart}>
              {weeklyData.map((week, index) => (
                <div key={index} className={styles.chartBar}>
                  <div 
                    className={styles.barFill}
                    style={{ 
                      height: `${week.quizScore}%`,
                      backgroundColor: week.quizScore >= averageScore ? '#10b981' : '#6b7280'
                    }}
                  ></div>
                  <span className={styles.barLabel}>W{index + 1}</span>
                  <span className={styles.barValue}>{week.quizScore}%</span>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.emptyChart}>
              <Brain className={styles.emptyIcon} />
              <p className={styles.emptyText}>Start taking quizzes to see your progress trends</p>
            </div>
          )}
        </div>
      </div>

      {/* Vocabulary Breakdown */}
      <div className={styles.vocabularySection}>
        <h3 className={styles.sectionTitle}>Vocabulary Progress by Sublist</h3>
        <div className={styles.sublistGrid}>
          {sublistProgress.length > 0 ? (
            sublistProgress.slice(0, 6).map((sublist) => (
              <div key={sublist.sublistNumber} className={styles.sublistCard}>
                <div className={styles.sublistHeader}>
                  <span className={styles.sublistName}>Sublist {sublist.sublistNumber}</span>
                  <span className={styles.sublistPercentage}>{sublist.masteryPercentage}%</span>
                </div>
                <div className={styles.sublistProgress}>
                  <div 
                    className={styles.sublistBar}
                    style={{ width: `${sublist.masteryPercentage}%` }}
                  ></div>
                </div>
                <div className={styles.sublistStats}>
                  <span className={styles.wordsLearned}>{sublist.wordsLearned} words</span>
                  <span className={styles.totalWords}>of {sublist.totalWords}</span>
                </div>
              </div>
            ))
          ) : (
            <div className={styles.emptyVocabulary}>
              <BookOpen className={styles.emptyIcon} />
              <p className={styles.emptyText}>Start exploring vocabulary to see progress breakdown</p>
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className={styles.actionSection}>
        <h3 className={styles.sectionTitle}>Performance Actions</h3>
        <div className={styles.actionButtons}>
          <button 
            className={styles.actionButton}
            onClick={onAnalyzeWeakAreas}
          >
            <TrendingUp className={styles.actionIcon} />
            <div className={styles.actionContent}>
              <span className={styles.actionTitle}>Analyze Weak Areas</span>
              <span className={styles.actionDescription}>Get personalized recommendations</span>
            </div>
          </button>
          
          <button 
            className={styles.actionButton}
            onClick={onViewDetailedStats}
          >
            <BarChart3 className={styles.actionIcon} />
            <div className={styles.actionContent}>
              <span className={styles.actionTitle}>Detailed Statistics</span>
              <span className={styles.actionDescription}>View comprehensive analytics</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};