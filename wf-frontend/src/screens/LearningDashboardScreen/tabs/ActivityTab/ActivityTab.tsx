import React from 'react';
import { Calendar, Clock, Trophy, BookOpen, Target, CheckCircle, Activity as ActivityIcon } from 'lucide-react';
import type { UnifiedDashboardData } from 'wf-shared/types';
import styles from './ActivityTab.module.css';

interface ActivityTabProps {
  data: UnifiedDashboardData;
  loading: boolean;
  onViewFullHistory: () => void;
  onFilterByActivity: (type: string) => void;
}

/**
 * ActivityTab component - Learning timeline and activity history
 * Purpose: Track learning patterns, achievements, and activity timeline
 * User Intent: "What have I been doing? When am I most productive?"
 */
export const ActivityTab: React.FC<ActivityTabProps> = ({
  data,
  loading,
  onViewFullHistory,
  onFilterByActivity
}) => {
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p className={styles.loadingText}>Loading your activity timeline...</p>
      </div>
    );
  }

  const recentActivity = data.recentActivity || [];
  const weeklyData = data.weeklyData || [];
  const achievements = data.achievements || [];

  // Calculate activity insights
  const totalActivities = recentActivity.length;
  const mostActiveDay = weeklyData.length > 0 
    ? weeklyData.reduce((max, day) => day.activitiesCompleted > max.activitiesCompleted ? day : max, weeklyData[0])
    : null;
  
  const avgDailyActivities = weeklyData.length > 0 
    ? weeklyData.reduce((sum, day) => sum + day.activitiesCompleted, 0) / weeklyData.length 
    : 0;

  // Group activities by date
  const groupedActivities = recentActivity.reduce((groups, activity) => {
    const date = new Date(activity.timestamp).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(activity);
    return groups;
  }, {} as Record<string, typeof recentActivity>);

  const activityDates = Object.keys(groupedActivities).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'quiz': return <Target className="h-4 w-4" />;
      case 'vocabulary': return <BookOpen className="h-4 w-4" />;
      case 'achievement': return <Trophy className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'quiz': return 'blue';
      case 'vocabulary': return 'green';
      case 'achievement': return 'yellow';
      default: return 'gray';
    }
  };

  return (
    <div 
      className={styles.activityTab}
      role="tabpanel"
      id="tabpanel-activity"
      aria-labelledby="tab-activity"
    >
      {/* Tab Description */}
      <div className={styles.tabDescription}>
        <p className={styles.tabDescriptionText}>
          Your complete learning timeline, study patterns, achievements, and activity history
        </p>
      </div>

      {/* Activity Summary */}
      <div className={styles.summarySection}>
        <h3 className={styles.sectionTitle}>Activity Summary</h3>
        <div className={styles.summaryGrid}>
          <div className={styles.summaryCard}>
            <div className={styles.summaryHeader}>
              <ActivityIcon className={`${styles.summaryIcon} ${styles.blue}`} />
              <span className={styles.summaryLabel}>Total Activities</span>
            </div>
            <div className={styles.summaryValue}>{totalActivities}</div>
            <div className={styles.summarySubtext}>this week</div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryHeader}>
              <Calendar className={`${styles.summaryIcon} ${styles.green}`} />
              <span className={styles.summaryLabel}>Most Active Day</span>
            </div>
            <div className={styles.summaryValue}>
              {mostActiveDay ? new Date(mostActiveDay.date).toLocaleDateString('en-US', { weekday: 'short' }) : 'N/A'}
            </div>
            <div className={styles.summarySubtext}>
              {mostActiveDay ? `${mostActiveDay.activitiesCompleted} activities` : 'No data yet'}
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryHeader}>
              <Clock className={`${styles.summaryIcon} ${styles.purple}`} />
              <span className={styles.summaryLabel}>Daily Average</span>
            </div>
            <div className={styles.summaryValue}>{avgDailyActivities.toFixed(1)}</div>
            <div className={styles.summarySubtext}>activities per day</div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryHeader}>
              <Trophy className={`${styles.summaryIcon} ${styles.orange}`} />
              <span className={styles.summaryLabel}>Achievements</span>
            </div>
            <div className={styles.summaryValue}>{achievements.length}</div>
            <div className={styles.summarySubtext}>badges earned</div>
          </div>
        </div>
      </div>

      {/* Weekly Activity Pattern */}
      <div className={styles.patternSection}>
        <h3 className={styles.sectionTitle}>Weekly Activity Pattern</h3>
        <div className={styles.patternChart}>
          {weeklyData.length > 0 ? (
            <div className={styles.dayBars}>
              {weeklyData.map((day, index) => {
                const dayName = new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' });
                const intensity = Math.min(day.activitiesCompleted / Math.max(avgDailyActivities * 2, 1), 1);
                
                return (
                  <div key={index} className={styles.dayBar}>
                    <div 
                      className={styles.activityBar}
                      style={{ 
                        height: `${Math.max(intensity * 100, 10)}%`,
                        backgroundColor: intensity > 0.8 ? '#10b981' : intensity > 0.5 ? '#3b82f6' : '#6b7280'
                      }}
                    ></div>
                    <span className={styles.dayLabel}>{dayName}</span>
                    <span className={styles.dayValue}>{day.activitiesCompleted}</span>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className={styles.emptyPattern}>
              <Calendar className={styles.emptyIcon} />
              <p className={styles.emptyText}>Start your learning journey to see activity patterns</p>
            </div>
          )}
        </div>
      </div>

      {/* Recent Activity Timeline */}
      <div className={styles.timelineSection}>
        <div className={styles.timelineHeader}>
          <h3 className={styles.sectionTitle}>Recent Activity</h3>
          <button 
            className={styles.viewAllButton}
            onClick={onViewFullHistory}
          >
            View Full History
          </button>
        </div>

        {activityDates.length > 0 ? (
          <div className={styles.timeline}>
            {activityDates.slice(0, 5).map((date) => (
              <div key={date} className={styles.timelineDay}>
                <div className={styles.dayHeader}>
                  <h4 className={styles.dayTitle}>
                    {new Date(date).toLocaleDateString('en-US', { 
                      weekday: 'long', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                  </h4>
                  <span className={styles.dayCount}>
                    {groupedActivities[date].length} {groupedActivities[date].length === 1 ? 'activity' : 'activities'}
                  </span>
                </div>

                <div className={styles.activityList}>
                  {groupedActivities[date].slice(0, 5).map((activity, index) => (
                    <div key={index} className={styles.activityItem}>
                      <div className={`${styles.activityIcon} ${styles[getActivityColor(activity.type)]}`}>
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className={styles.activityContent}>
                        <span className={styles.activityTitle}>{activity.title}</span>
                        <span className={styles.activityTime}>
                          {new Date(activity.timestamp).toLocaleTimeString('en-US', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </div>
                      {activity.score && (
                        <div className={styles.activityScore}>
                          {activity.score}%
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {groupedActivities[date].length > 5 && (
                    <button 
                      className={styles.showMoreButton}
                      onClick={() => onFilterByActivity(date)}
                    >
                      +{groupedActivities[date].length - 5} more activities
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.emptyTimeline}>
            <BookOpen className={styles.emptyIcon} />
            <h4 className={styles.emptyTitle}>No Recent Activity</h4>
            <p className={styles.emptyText}>
              Start taking quizzes or exploring vocabulary to see your activity timeline
            </p>
          </div>
        )}
      </div>

      {/* Recent Achievements */}
      {achievements.length > 0 && (
        <div className={styles.achievementsSection}>
          <h3 className={styles.sectionTitle}>Recent Achievements</h3>
          <div className={styles.achievementsList}>
            {achievements.slice(0, 3).map((achievement, index) => (
              <div key={index} className={styles.achievementCard}>
                <div className={styles.achievementIcon}>
                  <Trophy className="h-6 w-6 text-yellow-600" />
                </div>
                <div className={styles.achievementContent}>
                  <span className={styles.achievementName}>{achievement.name}</span>
                  <span className={styles.achievementDescription}>{achievement.description}</span>
                  <span className={styles.achievementDate}>
                    Earned {new Date(achievement.earnedAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};