/* Activity Tab Styles */

.activityTab {
  @apply space-y-8;
}

.tabDescription {
  @apply mb-6;
}

.tabDescriptionText {
  @apply text-gray-600 dark:text-gray-300 text-sm leading-relaxed;
}

.sectionTitle {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-6;
}

/* Loading States */
.loadingContainer {
  @apply flex flex-col items-center justify-center py-12;
}

.loadingSpinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4;
}

.loadingText {
  @apply text-gray-600 dark:text-gray-300;
}

/* Activity Summary Section */
.summarySection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.summaryGrid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.summaryCard {
  @apply bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg p-5 border border-gray-200 dark:border-gray-600;
}

.summaryHeader {
  @apply flex items-center space-x-3 mb-3;
}

.summaryIcon {
  @apply h-5 w-5;
}

.summaryIcon.blue {
  @apply text-blue-600;
}

.summaryIcon.green {
  @apply text-green-600;
}

.summaryIcon.purple {
  @apply text-purple-600;
}

.summaryIcon.orange {
  @apply text-orange-600;
}

.summaryLabel {
  @apply text-sm font-medium text-gray-600 dark:text-gray-300;
}

.summaryValue {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-1;
}

.summarySubtext {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* Pattern Section */
.patternSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.patternChart {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-6;
}

.dayBars {
  @apply flex items-end justify-center space-x-4 h-32;
}

.dayBar {
  @apply flex flex-col items-center space-y-2 flex-1 max-w-16;
}

.activityBar {
  @apply w-full rounded-t-md transition-all duration-1000 ease-out min-h-2;
}

.dayLabel {
  @apply text-xs font-medium text-gray-600 dark:text-gray-300;
}

.dayValue {
  @apply text-xs font-bold text-gray-900 dark:text-white;
}

.emptyPattern {
  @apply flex flex-col items-center justify-center py-12;
}

.emptyIcon {
  @apply h-12 w-12 text-gray-400 dark:text-gray-500 mb-4;
}

.emptyText {
  @apply text-gray-600 dark:text-gray-300 text-center;
}

/* Timeline Section */
.timelineSection {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.timelineHeader {
  @apply flex items-center justify-between mb-6;
}

.viewAllButton {
  @apply px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200;
}

.timeline {
  @apply space-y-6;
}

.timelineDay {
  @apply border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0 last:pb-0;
}

.dayHeader {
  @apply flex items-center justify-between mb-4;
}

.dayTitle {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.dayCount {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.activityList {
  @apply space-y-3;
}

.activityItem {
  @apply flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200;
}

.activityIcon {
  @apply p-2 rounded-full flex-shrink-0;
}

.activityIcon.blue {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400;
}

.activityIcon.green {
  @apply bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400;
}

.activityIcon.yellow {
  @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400;
}

.activityIcon.gray {
  @apply bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400;
}

.activityContent {
  @apply flex-1 min-w-0;
}

.activityTitle {
  @apply block font-medium text-gray-900 dark:text-white text-sm truncate;
}

.activityTime {
  @apply block text-xs text-gray-500 dark:text-gray-400;
}

.activityScore {
  @apply text-sm font-semibold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded;
}

.showMoreButton {
  @apply text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium ml-11;
}

.emptyTimeline {
  @apply flex flex-col items-center justify-center py-12;
}

.emptyTitle {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-2;
}

/* Achievements Section */
.achievementsSection {
  @apply bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-700;
}

.achievementsList {
  @apply space-y-4;
}

.achievementCard {
  @apply flex items-center space-x-4 bg-white dark:bg-gray-800 rounded-lg p-4 border border-yellow-200 dark:border-yellow-700;
}

.achievementIcon {
  @apply p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex-shrink-0;
}

.achievementContent {
  @apply flex-1 min-w-0;
}

.achievementName {
  @apply block font-semibold text-gray-900 dark:text-white;
}

.achievementDescription {
  @apply block text-sm text-gray-600 dark:text-gray-300 mb-1;
}

.achievementDate {
  @apply block text-xs text-gray-500 dark:text-gray-400;
}

/* Responsive Design */
@media (max-width: 768px) {
  .summaryGrid {
    @apply grid-cols-2 gap-4;
  }
  
  .dayBars {
    @apply space-x-2 h-24;
  }
  
  .timelineHeader {
    @apply flex-col items-start space-y-3;
  }
  
  .dayHeader {
    @apply flex-col items-start space-y-1;
  }
  
  .activityItem {
    @apply p-2;
  }
  
  .summarySection,
  .patternSection,
  .timelineSection,
  .achievementsSection {
    @apply p-4;
  }
}

@media (max-width: 640px) {
  .summaryGrid {
    @apply grid-cols-1;
  }
}

/* Animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.summaryCard,
.timelineDay,
.achievementCard {
  animation: slideUp 0.3s ease-out;
}

/* Stagger animation delays */
.summaryCard:nth-child(1) { animation-delay: 0.1s; }
.summaryCard:nth-child(2) { animation-delay: 0.2s; }
.summaryCard:nth-child(3) { animation-delay: 0.3s; }
.summaryCard:nth-child(4) { animation-delay: 0.4s; }

.timelineDay:nth-child(1) { animation-delay: 0.1s; }
.timelineDay:nth-child(2) { animation-delay: 0.2s; }
.timelineDay:nth-child(3) { animation-delay: 0.3s; }

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .summaryCard,
  .timelineDay,
  .achievementCard,
  .activityBar {
    animation: none;
    transition: none;
  }
}