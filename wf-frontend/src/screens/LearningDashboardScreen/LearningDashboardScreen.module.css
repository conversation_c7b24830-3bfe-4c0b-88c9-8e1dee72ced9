/* Unified Learning Dashboard Styles */

.containerWrapper {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.headerContainer {
  @apply mb-8;
}

.headerContent {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.title {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-2;
}

.subtitle {
  @apply text-gray-600 dark:text-gray-300;
}

/* Main Dashboard Grid Layout */
.dashboardGrid {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

/* Responsive breakpoints */
@media (min-width: 768px) {
  .dashboardGrid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .dashboardGrid {
    grid-template-columns: 2fr 1fr;
  }
}

/* Stats Cards Grid */
.statsGrid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.statCard {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6;
  transition: all 0.2s ease-in-out;
}

.statCard:hover {
  @apply shadow-md transform -translate-y-1;
}

.statCardIcon {
  @apply p-3 rounded-full;
}

.statCardIcon.blue {
  @apply bg-blue-100 dark:bg-blue-900/20;
}

.statCardIcon.green {
  @apply bg-green-100 dark:bg-green-900/20;
}

.statCardIcon.purple {
  @apply bg-purple-100 dark:bg-purple-900/20;
}

.statCardIcon.orange {
  @apply bg-orange-100 dark:bg-orange-900/20;
}

/* Progress Overview Section */
.progressOverview {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6;
}

.progressHeader {
  @apply flex items-center justify-between mb-6;
}

.progressTitle {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.progressIcon {
  @apply h-5 w-5 text-gray-400;
}

/* Weekly Progress Bar */
.progressBar {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4;
}

.progressFill {
  @apply bg-blue-600 h-3 rounded-full transition-all duration-500;
}

/* Learning Tools Panel */
.toolsPanel {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6;
}

.toolsHeader {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.toolsList {
  @apply space-y-3;
}

.toolButton {
  @apply w-full flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200;
  text-decoration: none;
}

.toolButton:hover {
  @apply border-blue-300 dark:border-blue-600;
}

.toolIcon {
  @apply h-5 w-5 mr-3;
}

.toolContent {
  @apply flex-1;
}

.toolName {
  @apply font-medium text-gray-900 dark:text-white;
}

.toolDescription {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* AWL Sublist Progress */
.sublistGrid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6;
}

.sublistCard {
  @apply border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800;
}

.sublistHeader {
  @apply flex justify-between items-center mb-2;
}

.sublistTitle {
  @apply font-medium text-gray-900 dark:text-white;
}

.sublistStats {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.sublistProgress {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1;
}

.sublistProgressFill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

.sublistPercentage {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* Recent Activity Timeline */
.activitySection {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mt-8;
}

.activityHeader {
  @apply flex items-center justify-between mb-6;
}

.activityTitle {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.activityTimeline {
  @apply space-y-4;
}

.activityItem {
  @apply border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer;
}

.activityItemHeader {
  @apply flex items-center justify-between mb-2;
}

.activityItemTitle {
  @apply font-medium text-gray-900 dark:text-white;
}

.activityItemMeta {
  @apply flex items-center text-sm text-gray-600 dark:text-gray-300 space-x-4;
}

.activityBadge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.activityBadge.quiz {
  @apply bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300;
}

.activityBadge.vocabulary {
  @apply bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300;
}

.activityBadge.word_study {
  @apply bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300;
}

/* Achievements Section */
.achievementsGrid {
  @apply space-y-4;
}

.achievementItem {
  @apply flex items-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg;
}

.achievementIcon {
  @apply p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-full mr-3;
}

.achievementContent {
  @apply flex-1;
}

.achievementName {
  @apply font-medium text-gray-900 dark:text-white;
}

.achievementDescription {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* Loading States */
.loadingContainer {
  @apply flex items-center justify-center h-96;
}

.loadingContent {
  @apply text-center;
}

.loadingSpinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4;
}

.loadingText {
  @apply text-gray-600 dark:text-gray-300;
}

/* Empty States */
.emptyState {
  @apply text-center py-12;
}

.emptyStateIcon {
  @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center;
}

.emptyStateTitle {
  @apply text-lg font-medium text-gray-900 dark:text-white mb-2;
}

.emptyStateDescription {
  @apply text-gray-600 dark:text-gray-300 mb-6;
}

.emptyStateAction {
  @apply bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200;
}

/* Weekly Activity Visualization */
.weeklyActivityDots {
  @apply flex space-x-1;
}

.activityDot {
  @apply w-3 h-3 rounded-full;
}

.activityDot.active {
  @apply bg-blue-600;
}

.activityDot.inactive {
  @apply bg-gray-200 dark:bg-gray-600;
}

/* Responsive utilities */
@media (max-width: 767px) {
  .statsGrid {
    @apply grid-cols-1 gap-4;
  }
  
  .sublistGrid {
    @apply grid-cols-1;
  }
  
  .containerWrapper {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  
  .headerContent {
    @apply px-4;
  }
  
  .dashboardGrid {
    @apply px-4;
  }
}