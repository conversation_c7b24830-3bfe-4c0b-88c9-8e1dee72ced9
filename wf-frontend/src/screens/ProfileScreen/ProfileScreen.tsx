import React from 'react';
import { User, Mail, Save } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Layout from "@/components/Layout";
import { useLanguage } from '@/context/LanguageContext';
import { useProfileHandler } from './ProfileScreen.handler';
import { LoadingState, LoadingButton } from '@/components/LoadingStates';
import AsyncErrorBoundary from '@/components/AsyncErrorBoundary';
import styles from './ProfileScreen.module.css';

const ProfileScreen: React.FC = () => {
  const { t } = useLanguage();
  const {
    formData,
    isEditing,
    loading,
    saving,
    error,
    levels,
    handleInputChange,
    handleSave,
    handleCancel,
    toggleEditing,
    retryLoad,
    hasChanges
  } = useProfileHandler();

  return (
    <Layout>
      <div className={styles.wrapper}>
        {/* Page Title */}
        <div className={styles.header}>
          <h2 className={styles.title}>{t('profile.title')}</h2>
          <p className={styles.subtitle}>{t('profile.subtitle')}</p>
        </div>

        <LoadingState
          loading={loading}
          error={error}
          onRetry={retryLoad}
          loadingComponent={
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-300">{t('profile.loadingProfile')}</p>
              </div>
            </div>
          }
          errorComponent={
            <AsyncErrorBoundary
              error={error}
              onRetry={retryLoad}
              onDismiss={() => {}}
            >
              <div></div>
            </AsyncErrorBoundary>
          }
        >
          {/* Error Message for inline errors */}
          {error && !loading && (
            <AsyncErrorBoundary
              error={error}
              onRetry={retryLoad}
              showErrorDetails={false}
            >
              <div></div>
            </AsyncErrorBoundary>
        )}

        {/* Profile Card */}
        <Card className={styles.profileCard}>
          <div className={styles.profileHeader}>
            <div className={styles.profileInfo}>
              <div className={styles.avatar}>
                <User className="h-6 w-6 text-gray-500 dark:text-gray-400" />
              </div>
              <div className={styles.profileDetails}>
                <h3 className={styles.profileName}>
                  {formData.firstName} {formData.lastName}
                </h3>
                <p className={styles.profileLevel}>{t('profile.level')}: {formData.levelName}</p>
              </div>
            </div>
            <Button
              onClick={toggleEditing}
              variant={isEditing ? "outline" : "default"}
              className={styles.editButton}
            >
              {isEditing ? t('common.cancel') : `${t('common.edit')} ${t('nav.profile')}`}
            </Button>
          </div>
        </Card>

        {/* Personal Information */}
        <Card className={styles.infoCard}>
          <CardHeader>
            <CardTitle className={styles.sectionTitle}>
              <User className="h-5 w-5 mr-2" />
              {t('profile.personalInfo')}
            </CardTitle>
          </CardHeader>
          <CardContent className={styles.formContent}>
            <div className={styles.formGrid}>
              <div className={styles.fieldGroup}>
                <label className={styles.fieldLabel}>{t('profile.firstName')}</label>
                {isEditing ? (
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={styles.textInput}
                    placeholder="Enter your first name"
                  />
                ) : (
                  <div className={styles.displayText}>{formData.firstName}</div>
                )}
              </div>

              <div className={styles.fieldGroup}>
                <label className={styles.fieldLabel}>{t('profile.lastName')}</label>
                {isEditing ? (
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={styles.textInput}
                    placeholder="Enter your last name"
                  />
                ) : (
                  <div className={styles.displayText}>{formData.lastName}</div>
                )}
              </div>

              <div className={styles.fieldGroup}>
                <label className={styles.fieldLabelWithIcon}>
                  <Mail className="h-4 w-4 inline mr-1" />
                  {t('profile.email')}
                </label>
                <div className={styles.displayText}>{formData.email}</div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 italic">{t('profile.emailNote')}</p>
              </div>

              <div className={styles.fieldGroup}>
                <label className={styles.fieldLabel}>{t('profile.level')}</label>
                {isEditing ? (
                  <select
                    name="level"
                    value={formData.level}
                    onChange={handleInputChange}
                    className={styles.selectInput}
                  >
                    <option value="">{t('profile.selectLevel')}</option>
                    {levels.map(level => (
                      <option key={level.id} value={level.id}>
                        {level.key} - {level.name}
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className={styles.displayText}>{formData.levelName}</div>
                )}
              </div>
            </div>

            {isEditing && (
              <div className={styles.actionButtonsContainer}>
                <button 
                  className={styles.cancelButton}
                  onClick={handleCancel} 
                  disabled={saving}
                >
                  {t('common.cancel')}
                </button>
                <LoadingButton
                  loading={saving}
                  onClick={handleSave}
                  className={styles.saveButton}
                  disabled={!hasChanges}
                  variant="primary"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t('profile.saveChanges')}
                </LoadingButton>
              </div>
            )}
          </CardContent>
        </Card>
        </LoadingState>
      </div>
    </Layout>
  );
};

export default ProfileScreen; 