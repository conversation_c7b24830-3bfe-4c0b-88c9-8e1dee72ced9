/**
 * ProfileScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 32 lines | AFTER: ~15 lines | REDUCTION: ~53%
 * 
 * Uses shared modules for:
 * - Container layouts (pageContainer)
 * - Typography patterns (pageTitle)
 */

/* Container Styles */
.wrapper {
  max-width: theme('maxWidth.2xl');
  margin: 0 auto;
  padding: theme('spacing.6');
  min-height: 100vh;
  background: white;
}

:global(.dark) .wrapper {
  background: theme('colors.gray.900');
}

.header {
  margin-bottom: theme('spacing.8');
}

.title {
  font-size: theme('fontSize.3xl');
  font-weight: theme('fontWeight.bold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.2');
}

:global(.dark) .title {
  color: theme('colors.gray.100');
}

.subtitle {
  font-size: theme('fontSize.base');
  color: theme('colors.gray.600');
  font-weight: theme('fontWeight.normal');
}

:global(.dark) .subtitle {
  color: theme('colors.gray.400');
}

/* Card Styles */
.profileCard {
  background: white;
  border-radius: theme('borderRadius.md');
  border: 1px solid theme('colors.gray.200');
  margin-bottom: theme('spacing.6');
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:global(.dark) .profileCard {
  background: theme('colors.gray.800');
  border: 1px solid theme('colors.gray.700');
}

/* Override any centering from Shadcn UI Card components */
.profileCard :global(.card-header) {
  text-align: left !important;
}

.profileCard :global(h3) {
  text-align: left !important;
}

/* Override Shadcn CardTitle centering */
.profileName :global(.card-title),
.profileName {
  text-align: left !important;
  margin: 0 !important;
}

.profileHeader {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: theme('spacing.6') !important;
  width: 100%;
  text-align: left !important;
}

.profileInfo {
  display: flex !important;
  align-items: center !important;
  gap: theme('spacing.4');
  flex: 1;
  justify-content: flex-start !important;
  text-align: left !important;
}

.avatar {
  width: theme('spacing.12');
  height: theme('spacing.12');
  background: theme('colors.gray.100');
  border-radius: theme('borderRadius.full');
  display: flex;
  align-items: center;
  justify-content: center;
}

:global(.dark) .avatar {
  background: theme('colors.gray.700');
}

.profileName {
  font-size: theme('fontSize.xl');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  margin-bottom: theme('spacing.1');
  text-align: left !important;
  justify-self: flex-start;
}

:global(.dark) .profileName {
  color: theme('colors.gray.100');
}

.profileLevel {
  color: theme('colors.gray.600');
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.normal');
  text-align: left !important;
  justify-self: flex-start;
}

:global(.dark) .profileLevel {
  color: theme('colors.gray.400');
}

.profileDetails {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  text-align: left !important;
  justify-content: flex-start !important;
}

.infoCard {
  background: white;
  border-radius: theme('borderRadius.md');
  border: 1px solid theme('colors.gray.200');
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:global(.dark) .infoCard {
  background: theme('colors.gray.800');  
  border: 1px solid theme('colors.gray.700');
}

.sectionTitle {
  display: flex;
  align-items: center;
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.semibold');
  color: theme('colors.gray.900');
  padding: theme('spacing.6');
  border-bottom: 1px solid theme('colors.gray.200');
}

:global(.dark) .sectionTitle {
  color: theme('colors.gray.100');
  border-bottom: 1px solid theme('colors.gray.700');
}

.formContent {
  padding: theme('spacing.6');
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: theme('spacing.4');
}

@media (max-width: theme('screens.md')) {
  .formGrid {
    grid-template-columns: 1fr;
  }
}

/* Form Field Styles */
.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.2');
}

.fieldLabel {
  display: block;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
  color: theme('colors.gray.700');
  margin-bottom: theme('spacing.1');
}

:global(.dark) .fieldLabel {
  color: theme('colors.gray.300');
}

.fieldLabelWithIcon {
  composes: fieldLabel;
  display: flex;
  align-items: center;
}

.textInput {
  width: 100%;
  padding: theme('spacing.3');
  border: 1px solid theme('colors.gray.300');
  border-radius: theme('borderRadius.md');
  font-size: theme('fontSize.base');
  background: white;
  color: theme('colors.gray.900');
}

.textInput:focus {
  outline: none;
  border-color: theme('colors.blue.500');
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:global(.dark) .textInput {
  background: theme('colors.gray.700');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.100');
}

:global(.dark) .textInput:focus {
  border-color: theme('colors.blue.400');
}

.selectInput {
  composes: textInput;
  cursor: pointer;
}

.displayText {
  padding: theme('spacing.3');
  font-size: theme('fontSize.base');
  color: theme('colors.gray.900');
  background: theme('colors.gray.50');
  border-radius: theme('borderRadius.md');
  min-height: theme('spacing.12');
  display: flex;
  align-items: center;
}

:global(.dark) .displayText {
  color: theme('colors.gray.100');
  background: theme('colors.gray.700');
}

/* Button Styles */
.editButton {
  padding: theme('spacing.2') theme('spacing.4');
  border-radius: theme('borderRadius.md');
  font-weight: theme('fontWeight.medium');
  font-size: theme('fontSize.sm');
  border: 1px solid theme('colors.gray.300');
  background: white;
  color: theme('colors.gray.700');
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  align-self: center;
}

.editButton:hover {
  background: theme('colors.gray.50');
  border-color: theme('colors.gray.400');
}

:global(.dark) .editButton {
  background: theme('colors.gray.800');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.200');
}

:global(.dark) .editButton:hover {
  background: theme('colors.gray.700');
  border-color: theme('colors.gray.500');
}

.actionButtonsContainer {
  display: flex;
  gap: theme('spacing.3');
  margin-top: theme('spacing.6');
  justify-content: flex-end;
}

.saveButton {
  padding: theme('spacing.2') theme('spacing.6');
  border-radius: theme('borderRadius.md');
  font-weight: theme('fontWeight.medium');
  font-size: theme('fontSize.sm');
  border: none;
  background: theme('colors.blue.600');
  color: white;
  cursor: pointer;
}

.saveButton:hover:not(:disabled) {
  background: theme('colors.blue.700');
}

.saveButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cancelButton {
  padding: theme('spacing.2') theme('spacing.4');
  border-radius: theme('borderRadius.md');
  font-weight: theme('fontWeight.medium');
  font-size: theme('fontSize.sm');
  border: 1px solid theme('colors.gray.300');
  background: white;
  color: theme('colors.gray.700');
  cursor: pointer;
}

.cancelButton:hover {
  background: theme('colors.gray.50');
}

:global(.dark) .cancelButton {
  background: theme('colors.gray.800');
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.300');
}

:global(.dark) .cancelButton:hover {
  background: theme('colors.gray.700');
}

/* Responsive Design */
@media (max-width: theme('screens.md')) {
  .wrapper {
    padding: theme('spacing.4');
  }

  .profileHeader {
    flex-direction: column;
    text-align: center;
    gap: theme('spacing.4');
  }

  .profileInfo {
    flex-direction: column;
    text-align: center;
    gap: theme('spacing.3');
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: theme('spacing.4');
  }

  .actionButtonsContainer {
    flex-direction: column-reverse;
    gap: theme('spacing.2');
  }

  .saveButton,
  .cancelButton {
    width: 100%;
    text-align: center;
  }
}