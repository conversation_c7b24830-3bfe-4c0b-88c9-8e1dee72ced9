import { useState, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { VocabularyService } from '@/services/vocabularyService';
import type { 
  WordSearchResult,
  WordDetails,
  WordQuiz,
  WordQuizAttempt
} from '@/types';


/**
 * Handler hook for WordMasterScreen component
 * Manages word search, details display, and quiz interactions
 */
export const useWordMasterScreen = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResult, setSearchResult] = useState<WordSearchResult | null>(null);
  const [wordDetails, setWordDetails] = useState<WordDetails | null>(null);
  const [currentQuiz, setCurrentQuiz] = useState<WordQuiz | null>(null);
  const [showQuiz, setShowQuiz] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  /**
   * Handle word search
   */
  const handleSearch = useCallback(async (queryOverride?: string) => {
    const query = queryOverride || searchQuery;
    if (!query.trim()) {
      setError('Please enter a word to search');
      return;
    }

    setError(null);
    setSearchResult(null);
    setWordDetails(null);
    setCurrentQuiz(null);
    setShowQuiz(false);
    setIsSearching(true);

    try {
      // Update search query if override provided
      if (queryOverride) {
        setSearchQuery(queryOverride);
      }
      
      // Execute search directly through service
      console.log('Searching for:', query);
      const result = await VocabularyService.searchWords({
        query,
        includeWordForms: true,
        mode: 'contains'
      });
      console.log('Search result:', result);
      console.log('Result success:', result.success);
      console.log('Result data:', result.data);
      console.log('Result error:', result.error);
      
      if (result.data && !result.error) {
        console.log('Setting search result:', result.data);
        setSearchResult(result.data);
        
        if (result.data.found && result.data.word) {
          console.log('Found word, setting details:', result.data.word);
          console.log('Word details structure:', {
            word: result.data.word.word,
            definitionsByForm: result.data.word.definitionsByForm,
            buildingNotes: result.data.word.buildingNotes,
            quiz: result.data.word.quiz
          });
          setWordDetails(result.data.word);
          setCurrentQuiz(result.data.word.quiz);
        } else {
          console.log('Word not found or no word data');
          console.log('result.data.found:', result.data.found);
          console.log('result.data.word:', result.data.word);
        }
      } else if (result.error) {
        console.log('Search error:', result.error);
        setError(result.error.message || 'Search failed. Please try again.');
      } else {
        console.log('No result data and no error');
      }
    } catch (err) {
      setError('Search failed. Please check your connection and try again.');
      console.error('Search error:', err);
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery]);

  /**
   * Handle suggestion click
   */
  const handleSuggestionClick = useCallback((suggestion: string) => {
    setSearchQuery(suggestion);
    handleSearch(suggestion);
  }, [handleSearch]);

  /**
   * Handle word selection from sidebar
   */
  const handleWordSelect = useCallback((word: string) => {
    setSearchQuery(word);
    handleSearch(word);
  }, [handleSearch]);

  /**
   * Start interactive quiz
   */
  const handleStartQuiz = useCallback(() => {
    if (currentQuiz && currentQuiz.questions.length > 0) {
      setShowQuiz(true);
    }
  }, [currentQuiz]);

  /**
   * Complete quiz and return to word details
   */
  const handleQuizComplete = useCallback(async (attempt: WordQuizAttempt) => {
    setShowQuiz(false);
    
    if (attempt && user?.id && wordDetails?.word.id) {
      try {
        // Save quiz attempt to database
        const response = await VocabularyService.saveWordQuizAttempt(
          user.id, 
          wordDetails.word.id, 
          {
            score: attempt.score,
            totalQuestions: attempt.totalQuestions,
            correctAnswers: attempt.correctAnswers,
            timeSpent: attempt.timeSpent,
            answers: attempt.answers,
            completedAt: new Date().toISOString()
          }
        );
        
        if (response.error) {
          console.error('Failed to save quiz attempt:', response.error);
        } else {
          console.log('Quiz attempt saved successfully');
        }
      } catch (error) {
        console.error('Error saving quiz attempt:', error);
      }
    }
  }, [user?.id, wordDetails?.word.id]);

  /**
   * Clear search results and reset state
   */
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResult(null);
    setWordDetails(null);
    setCurrentQuiz(null);
    setShowQuiz(false);
    setError(null);
  }, []);

  /**
   * Get word progress (for future use)
   */
  const getWordProgress = useCallback(async (_wordId: string) => {
    // TODO: Implement word progress fetching
    // This would get user's study progress for a specific word
    return null;
  }, []);

  /**
   * Save word for later study (bookmark)
   */
  const bookmarkWord = useCallback(async (_wordId: string) => {
    // TODO: Implement word bookmarking
    // This would save the word to user's study list
    return null;
  }, []);

  /**
   * Mark word as studied
   */
  const markWordStudied = useCallback(async (_wordId: string) => {
    // TODO: Implement progress tracking
    // This would update user's vocabulary progress
    return null;
  }, []);

  /**
   * Handle word pronunciation using text-to-speech or external API
   */
  const handlePronounce = useCallback(async (word: string) => {
    try {
      // Option 1: Use browser's built-in Speech Synthesis API
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(word);
        utterance.rate = 0.8; // Slightly slower for learning
        utterance.pitch = 1;
        utterance.volume = 1;
        
        // Try to use a high-quality English voice
        const voices = speechSynthesis.getVoices();
        const englishVoice = voices.find(voice => 
          voice.lang.startsWith('en') && 
          (voice.name.includes('Enhanced') || voice.name.includes('Premium') || voice.name.includes('Google'))
        ) || voices.find(voice => voice.lang.startsWith('en'));
        
        if (englishVoice) {
          utterance.voice = englishVoice;
        }
        
        speechSynthesis.speak(utterance);
        return true;
      }
      
      // Option 2: Could integrate with external TTS services like:
      // - Eleven Labs API
      // - Google Cloud Text-to-Speech
      // - Amazon Polly
      // - Azure Cognitive Services
      
      console.warn('Speech synthesis not supported in this browser');
      return false;
    } catch (error) {
      console.error('Error pronouncing word:', error);
      return false;
    }
  }, []);

  /**
   * Get IPA pronunciation from current word details
   */
  const getCurrentPronunciation = useCallback((_word: string) => {
    if (!wordDetails) return null;
    
    // Look for IPA pronunciation in definitions
    for (const definitions of Object.values(wordDetails.definitionsByForm)) {
      for (const def of definitions) {
        if (def.ipa_pronunciation) {
          return def.ipa_pronunciation;
        }
      }
    }
    return null;
  }, [wordDetails]);

  /**
   * Handle pronunciation with fallback to dictionary API audio
   */
  const handlePronounceWithFallback = useCallback(async (word: string) => {
    try {
      // First try browser TTS
      const ttsSuccess = await handlePronounce(word);
      if (ttsSuccess) return;
      
      // Fallback: Try to get audio from a free dictionary API
      try {
        const response = await fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${word}`);
        if (response.ok) {
          const data = await response.json();
          const phonetics = data[0]?.phonetics;
          const audioUrl = phonetics?.find((p: { audio?: string }) => p.audio)?.audio;
          
          if (audioUrl) {
            const audio = new Audio(audioUrl);
            await audio.play();
            return;
          }
        }
      } catch (apiError) {
        console.log('Dictionary API fallback failed:', apiError);
      }
      
      // Last fallback: Show pronunciation text if available
      alert(`Pronunciation not available. IPA: ${getCurrentPronunciation(word)}`);
    } catch (error) {
      console.error('Pronunciation failed:', error);
    }
  }, [handlePronounce, getCurrentPronunciation]);

  return {
    // State
    searchQuery,
    setSearchQuery,
    searchResult,
    isSearching,
    error,
    wordDetails,
    showQuiz,
    setShowQuiz,
    currentQuiz,
    showSidebar,
    setShowSidebar,
    
    // Actions
    handleSearch,
    handleSuggestionClick,
    handleWordSelect,
    handleStartQuiz, 
    handleQuizComplete,
    clearSearch,
    
    // Utility functions
    getWordProgress,
    bookmarkWord,
    markWordStudied,
    handlePronounce,
    handlePronounceWithFallback,
    getCurrentPronunciation
  };
};