import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Book<PERSON>pen, ArrowLeft, Search, Play, Clock, Target, Lightbulb, Menu, X } from 'lucide-react';

import TopNavigation from '@/components/TopNavigation';
import { WordQuiz } from '@/components/WordQuiz';
import { PronunciationPlayer } from '@/components/PronunciationPlayer';
import { AwlSublistSidebar } from '@/components/AwlSublistSidebar';
import { useWordMasterScreen } from './WordMasterScreen.handler';
import styles from './WordMasterScreen.module.css';

const WordMasterScreen: React.FC = () => {
  const {
    searchQuery,
    setSearchQuery,
    searchResult,
    isSearching,
    error,
    wordDetails,
    showQuiz,
    setShowQuiz,
    currentQuiz,
    handleSearch,
    handleSuggestionClick,
    handleStartQuiz,
    handleQuizComplete,
    clearSearch,
    getCurrentPronunciation,
    handleWordSelect,
    showSidebar,
    setShowSidebar
  } = useWordMasterScreen();

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      
      <div className="flex h-[calc(100vh-64px)]"> {/* Assuming TopNavigation is 64px */}
        {/* Main Content */}
        <div className={`flex-1 overflow-auto transition-all duration-300 ${showSidebar ? 'lg:mr-80' : ''}`}>
          <div className="max-w-5xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            to="/vocabulary-builder" 
            className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Dashboard
          </Link>
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
              <BookOpen className="h-8 w-8 mr-3 text-green-600" />
              Word Master
            </h1>
            {/* Sidebar Toggle */}
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
              title={showSidebar ? 'Hide AWL Words' : 'Show AWL Words'}
            >
              {showSidebar ? (
                <X className="h-5 w-5 text-gray-600" />
              ) : (
                <Menu className="h-5 w-5 text-gray-600" />
              )}
            </button>
          </div>
          <p className="text-gray-600">
            Search AWL words and master them with interactive quizzes and comprehensive information
          </p>
        </div>

        {/* Search Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  placeholder="Search for any AWL word (e.g., analyze, establish, concept...)"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  disabled={isSearching}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handleSearch()}
                disabled={!searchQuery.trim() || isSearching}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {isSearching ? 'Searching...' : 'Search'}
              </button>
              {(searchResult || error) && (
                <button
                  onClick={clearSearch}
                  className="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Clear
                </button>
              )}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Search Suggestions */}
          {searchResult && !searchResult.found && searchResult.suggestions && searchResult.suggestions.length > 0 && (
            <div className="mt-4">
              <p className="text-gray-600 mb-2">Word not found. Did you mean:</p>
              <div className="flex flex-wrap gap-2">
                {searchResult.suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full hover:bg-blue-100 transition-colors text-sm"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Word Details */}
        {wordDetails && !showQuiz && (
          <div className="space-y-6">
            {/* Word Header */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-1">
                    {wordDetails.word.word}
                  </h2>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>AWL Sublist {wordDetails.word.sublists}</span>
                    <span>Frequency Rank: {wordDetails.word.frequency_rank}</span>
                    {wordDetails.word.word_forms && (
                      <span>Forms: {
                        typeof wordDetails.word.word_forms === 'object' 
                          ? Object.values(wordDetails.word.word_forms).join(', ')
                          : wordDetails.word.word_forms
                      }</span>
                    )}
                  </div>
                </div>
                <div className="flex gap-3 mt-4 md:mt-0">
                  <PronunciationPlayer
                    word={wordDetails.word.word}
                    ipa={getCurrentPronunciation(wordDetails.word.word) || undefined}
                    size="md"
                    showIPA={false}
                  />
                  <button
                    onClick={handleStartQuiz}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Take Quiz
                  </button>
                </div>
              </div>
            </div>

            {/* Definitions by Word Form */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">📖 Definitions</h3>
              <div className="space-y-6">
                {Object.entries(wordDetails.definitionsByForm).map(([form, definitions]) => (
                  <div key={form} className={styles.definitionSection}>
                    <h4 className="font-medium text-gray-800 mb-3 capitalize">
                      {form} {definitions.length > 1 && `(${definitions.length} definitions)`}
                    </h4>
                    <div className="space-y-4">
                      {definitions.map((def: Record<string, unknown>, index: number) => (
                        <div key={index} className="border-l-4 border-green-200 pl-4">
                          <div className="flex items-start justify-between mb-2">
                            <p className="text-gray-900">{String(def.english_definition || def.definition || '')}</p>
                            <div className="flex items-center gap-2 ml-4">
                              {def.word_text && (
                                <PronunciationPlayer
                                  word={String(def.word_text)}
                                  ipa={def.ipa_pronunciation ? String(def.ipa_pronunciation) : undefined}
                                  size="sm"
                                  showIPA={true}
                                />
                              )}
                            </div>
                          </div>
                          {def.vietnamese_meaning && (
                            <p className="text-gray-600 text-sm mb-2">
                              🇻🇳 {String(def.vietnamese_meaning)}
                            </p>
                          )}
                          {def.example_sentences && Array.isArray(def.example_sentences) && (
                            <div className="text-gray-700 italic mb-2">
                              {(def.example_sentences as string[]).map((sentence: string, idx: number) => (
                                <p key={idx} className="mb-1">
                                  &ldquo;{sentence}&rdquo;
                                </p>
                              ))}
                            </div>
                          )}
                          {def.example_sentence && (
                            <p className="text-gray-700 italic mb-2">
                              &ldquo;{String(def.example_sentence)}&rdquo;
                            </p>
                          )}
                          {def.etymology && (
                            <p className="text-gray-500 text-sm mt-2">
                              📚 Etymology: {
                                typeof def.etymology === 'object' 
                                  ? JSON.stringify(def.etymology)
                                  : String(def.etymology)
                              }
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Word Building Notes */}
            {wordDetails.buildingNotes && wordDetails.buildingNotes.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Lightbulb className="h-5 w-5 mr-2 text-yellow-500" />
                  Learning Tips
                </h3>
                <div className="space-y-4">
                  {wordDetails.buildingNotes.map((note: Record<string, unknown>, index: number) => (
                    <div key={index} className="p-4 bg-yellow-50 rounded-lg">
                      <h4 className="font-medium text-gray-800 mb-2">{String(note.note_type || '')}</h4>
                      <p className="text-gray-700">{String(note.content || '')}</p>
                      {note.examples && (
                        <div className="mt-2">
                          <p className="text-sm font-medium text-gray-600 mb-1">Examples:</p>
                          <p className="text-gray-600 text-sm">{String(note.examples)}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Quiz Preview */}
            {currentQuiz && currentQuiz.questions.length > 0 && (
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    <Target className="h-5 w-5 mr-2 text-green-600" />
                    Interactive Quiz Ready
                  </h3>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-1" />
                    ~{Math.ceil(currentQuiz.estimatedTime / 60)} min
                  </div>
                </div>
                <p className="text-gray-700 mb-4">
                  Test your understanding of &ldquo;{wordDetails.word.word}&rdquo; with {currentQuiz.totalQuestions} interactive questions.
                </p>
                <button
                  onClick={handleStartQuiz}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Start Quiz Now
                </button>
              </div>
            )}
          </div>
        )}

        {/* Quiz Interface */}
        {showQuiz && currentQuiz && wordDetails && (
          <WordQuiz
            quiz={currentQuiz}
            wordDetails={wordDetails}
            onComplete={handleQuizComplete}
            onBack={() => setShowQuiz(false)}
          />
        )}

        {/* Empty State */}
        {!wordDetails && !searchResult && !error && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Explore AWL Words</h3>
            <p className="text-gray-600 mb-6">
              Search words below or {showSidebar ? 'browse the AWL sublists on the right' : 'click the menu button to browse AWL sublists'} to discover words to study.
            </p>
            <div className="flex flex-wrap justify-center gap-2">
              {['analyze', 'establish', 'concept', 'significant', 'approach', 'required'].map((word) => (
                <button
                  key={word}
                  onClick={() => {
                    setSearchQuery(word);
                    handleSearch(word);
                  }}
                  className="px-3 py-1 bg-green-50 text-green-700 rounded-full hover:bg-green-100 transition-colors text-sm"
                >
                  {word}
                </button>
              ))}
            </div>
          </div>
        )}
          </div>
        </div>

        {/* AWL Sublist Sidebar */}
        {showSidebar && (
          <>
            {/* Mobile Overlay */}
            <div 
              className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
              onClick={() => setShowSidebar(false)}
            />
            {/* Sidebar */}
            <div className="fixed right-0 top-16 w-80 h-[calc(100vh-64px)] z-30 lg:z-10">
              <AwlSublistSidebar
                onWordSelect={handleWordSelect}
                selectedWord={wordDetails?.word.word}
                className="h-full"
                onMobileClose={() => setShowSidebar(false)}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default WordMasterScreen;