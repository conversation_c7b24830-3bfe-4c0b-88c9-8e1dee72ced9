/* WordMasterScreen CSS Module */

.searchContainer {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8;
}

.searchInput {
  @apply w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg;
  @apply focus:ring-2 focus:ring-green-500 focus:border-transparent;
  transition: all 0.2s ease-in-out;
}

.searchInput:disabled {
  @apply bg-gray-50 cursor-not-allowed;
}

.searchButton {
  @apply px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700;
  @apply disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors;
}

.clearButton {
  @apply px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors;
}

.suggestionChip {
  @apply px-3 py-1 bg-blue-50 text-blue-700 rounded-full hover:bg-blue-100;
  @apply transition-colors text-sm cursor-pointer;
}

.wordHeader {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.wordTitle {
  @apply text-2xl font-bold text-gray-900 mb-1;
}

.wordMetadata {
  @apply flex items-center gap-4 text-sm text-gray-600;
}

.actionButton {
  @apply flex items-center px-3 py-2 rounded-lg transition-colors;
}

.actionButton.primary {
  @apply bg-green-600 text-white hover:bg-green-700;
}

.actionButton.secondary {
  @apply bg-blue-50 text-blue-700 hover:bg-blue-100;
}

.definitionSection {
  @apply mb-6;
}

.definitionCard {
  @apply border-l-4 border-green-200 pl-4 mb-4;
  transition: all 0.2s ease-in-out;
}

.definitionCard:hover {
  @apply border-green-300;
  transform: translateX(2px);
}

.definitionHeader {
  @apply flex items-start justify-between mb-2;
}

.definitionText {
  @apply text-gray-900 leading-relaxed;
}

.pronunciation {
  @apply text-blue-600 font-mono text-sm ml-4 flex-shrink-0;
}

.vietnameseTranslation {
  @apply text-gray-600 text-sm mb-2;
}

.exampleSentence {
  @apply text-gray-700 italic;
}

.etymology {
  @apply text-gray-500 text-sm mt-2;
}

.learningNote {
  @apply p-4 bg-yellow-50 rounded-lg;
  transition: all 0.2s ease-in-out;
}

.learningNote:hover {
  @apply bg-yellow-100;
  transform: translateY(-1px);
}

.noteType {
  @apply font-medium text-gray-800 mb-2;
}

.noteContent {
  @apply text-gray-700;
}

.noteExamples {
  @apply mt-2;
}

.quizPreview {
  @apply bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200 p-6;
}

.quizHeader {
  @apply flex items-center justify-between mb-4;
}

.quizTitle {
  @apply text-lg font-semibold text-gray-900 flex items-center;
}

.quizEstimate {
  @apply flex items-center text-sm text-gray-600;
}

.quizDescription {
  @apply text-gray-700 mb-4;
}

.startQuizButton {
  @apply px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors;
}

.quizInterface {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.quizContent {
  @apply text-center;
}

.quizIcon {
  @apply h-12 w-12 text-green-600 mx-auto mb-4;
}

.quizOverviewTitle {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.quizFeatureList {
  @apply text-left max-w-md mx-auto space-y-2 mb-6;
}

.quizFeature {
  @apply flex items-center;
}

.featureDot {
  @apply w-2 h-2 bg-green-600 rounded-full mr-3;
}

.backButton {
  @apply px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors;
}

.emptyState {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center;
}

.emptyIcon {
  @apply h-16 w-16 text-gray-400 mx-auto mb-4;
}

.emptyTitle {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.emptyDescription {
  @apply text-gray-600 mb-6;
}

.wordSampleChips {
  @apply flex flex-wrap justify-center gap-2;
}

.sampleWordChip {
  @apply px-3 py-1 bg-green-50 text-green-700 rounded-full hover:bg-green-100;
  @apply transition-colors text-sm cursor-pointer;
}

.errorMessage {
  @apply mt-4 p-3 bg-red-50 border border-red-200 rounded-lg;
}

.errorText {
  @apply text-red-700 text-sm;
}

.loadingSpinner {
  @apply animate-spin rounded-full h-5 w-5 border-b-2 border-green-600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .searchContainer {
    @apply p-4;
  }
  
  .wordHeader {
    @apply p-4;
  }
  
  .wordMetadata {
    @apply flex-col items-start gap-2;
  }
  
  .actionButton {
    @apply w-full justify-center;
  }
  
  .quizHeader {
    @apply flex-col items-start gap-2;
  }
  
  .definitionHeader {
    @apply flex-col items-start gap-2;
  }
  
  .pronunciation {
    @apply ml-0;
  }
}

/* Animation classes */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideIn {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fadeIn {
  animation: fadeIn 0.2s ease-out;
}

/* Focus styles for accessibility */
.searchInput:focus,
.searchButton:focus,
.clearButton:focus,
.suggestionChip:focus,
.actionButton:focus,
.startQuizButton:focus,
.backButton:focus,
.sampleWordChip:focus {
  @apply outline-none ring-2 ring-offset-2;
}

.searchInput:focus {
  @apply ring-green-500;
}

.searchButton:focus,
.startQuizButton:focus {
  @apply ring-green-500;
}

.clearButton:focus {
  @apply ring-gray-500;
}

.suggestionChip:focus {
  @apply ring-blue-500;
}

.actionButton.secondary:focus {
  @apply ring-blue-500;
}

.backButton:focus {
  @apply ring-gray-500;
}

.sampleWordChip:focus {
  @apply ring-green-500;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .searchContainer,
  .wordHeader,
  .quizInterface,
  .emptyState {
    @apply bg-gray-800 border-gray-700;
  }
  
  .wordTitle,
  .quizOverviewTitle,
  .emptyTitle {
    @apply text-white;
  }
  
  .definitionText,
  .noteContent,
  .quizDescription,
  .emptyDescription {
    @apply text-gray-300;
  }
  
  .wordMetadata,
  .vietnameseTranslation,
  .exampleSentence,
  .etymology {
    @apply text-gray-400;
  }
  
  .learningNote {
    @apply bg-yellow-900 bg-opacity-20;
  }
  
  .noteType {
    @apply text-gray-200;
  }
}