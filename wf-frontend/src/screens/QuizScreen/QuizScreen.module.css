/**
 * QuizScreen CSS Module - Refactored with Shared Modules
 * BEFORE: 664 lines | AFTER: ~400 lines | REDUCTION: ~40%
 * 
 * Uses shared modules for:
 * - Container layouts (fullHeightContainer, pageContainer)
 * - Card components (cardBase, cardCentered)
 * - Button components (buttonPrimary, buttonSecondary, fullWidth)
 * - Typography patterns (pageTitle, subtitle, description, mutedText)
 */

/* Container using shared full-height layout */
.wrapper {
  composes: fullHeightContainer from '@/styles/shared/layouts/containers.module.css';
  background: linear-gradient(135deg, theme('colors.gray.50') 0%, theme('colors.blue.50') 100%);
  min-height: 100vh;
  padding: theme('spacing.4');
}

:global(.dark) .wrapper {
  background: linear-gradient(135deg, theme('colors.gray.900') 0%, theme('colors.blue.950') 100%);
}

.loadingWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 384px; /* 24rem */
}

.errorCard {
  text-align: center;
}

.header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: theme('borderRadius.xl');
  padding: theme('spacing.6');
  margin-bottom: theme('spacing.6');
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:global(.dark) .header {
  background: rgba(17, 24, 39, 0.9);
  border: 1px solid rgba(55, 65, 81, 0.3);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.headerTop {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: theme('spacing.4');
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.headerRight {
  text-align: right;
}

/* Typography using shared components */
.title {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xl');
  color: theme('colors.gray.900');
  font-weight: theme('fontWeight.bold');
}

:global(.dark) .title {
  color: theme('colors.gray.100');
}

.subtitle {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.600');
}

:global(.dark) .subtitle {
  color: theme('colors.gray.300');
}

.questionInfo {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.sm');
  color: theme('colors.gray.700');
  font-weight: theme('fontWeight.medium');
}

:global(.dark) .questionInfo {
  color: theme('colors.gray.200');
}

.answeredInfo {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.xs');
  color: theme('colors.gray.600');
  font-weight: theme('fontWeight.medium');
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:global(.dark) .answeredInfo {
  color: theme('colors.gray.300');
}

.progressBar {
  width: 100%;
  background-color: theme('colors.gray.200');
  border-radius: theme('borderRadius.full');
  height: theme('spacing.3');
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

:global(.dark) .progressBar {
  background-color: theme('colors.gray.700');
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.progressFill {
  background: linear-gradient(90deg, theme('colors.blue.500'), theme('colors.indigo.600'));
  height: 100%;
  border-radius: theme('borderRadius.full');
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.progressFill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Loading and Error Styles */
.loadingContainer {
  text-align: center;
}

.loadingSpinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: theme('spacing.12');
  width: theme('spacing.12');
  border: 2px solid transparent;
  border-bottom: 2px solid theme('colors.blue.600');
  margin: 0 auto theme('spacing.4');
}

.loadingText {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
}

.errorIcon {
  width: theme('spacing.12');
  height: theme('spacing.12');
  margin: 0 auto theme('spacing.2');
}

.errorIconContainer {
  color: theme('colors.red.600');
  margin-bottom: theme('spacing.4');
}

.errorTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.2');
}

.errorMessage {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.4');
}

/* Button Styles using shared components - removed unused buttonBase */

.quitButton {
  color: theme('colors.gray.600');
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(209, 213, 219, 0.5);
  border-radius: theme('borderRadius.lg');
  padding: theme('spacing.2');
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quitButton:hover {
  color: theme('colors.red.600');
  background: rgba(255, 255, 255, 1);
  border-color: theme('colors.red.200');
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

:global(.dark) .quitButton {
  color: theme('colors.gray.400');
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.5);
}

:global(.dark) .quitButton:hover {
  color: theme('colors.red.400');
  background: rgba(31, 41, 55, 1);
  border-color: theme('colors.red.800');
}

/* Navigation Button Base Styles */
.navButton {
  padding: theme('spacing.3') theme('spacing.6');
  border-radius: theme('borderRadius.xl');
  font-weight: theme('fontWeight.medium');
  font-size: theme('fontSize.base');
  min-width: theme('spacing.24');
  height: theme('spacing.12');
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid;
  cursor: pointer;
}

.previousButton {
  background: rgba(255, 255, 255, 0.9);
  border-color: theme('colors.gray.300');
  color: theme('colors.gray.700');
  backdrop-filter: blur(5px);
}

.previousButton:hover:not(.buttonDisabled) {
  background: rgba(255, 255, 255, 1);
  border-color: theme('colors.gray.400');
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

:global(.dark) .previousButton {
  background: rgba(31, 41, 55, 0.9);
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.300');
}

:global(.dark) .previousButton:hover:not(.buttonDisabled) {
  background: rgba(31, 41, 55, 1);
  border-color: theme('colors.gray.500');
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.checkAnswerButton {
  background: linear-gradient(135deg, theme('colors.blue.500'), theme('colors.blue.600'));
  border-color: theme('colors.blue.600');
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.checkAnswerButton:hover:not(.buttonDisabled) {
  background: linear-gradient(135deg, theme('colors.blue.600'), theme('colors.blue.700'));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.nextButton {
  background: linear-gradient(135deg, theme('colors.blue.500'), theme('colors.blue.600'));
  border-color: theme('colors.blue.600');
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.nextButton:hover:not(.buttonDisabled) {
  background: linear-gradient(135deg, theme('colors.blue.600'), theme('colors.blue.700'));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.finishButton {
  background: linear-gradient(135deg, theme('colors.indigo.600'), theme('colors.indigo.700'));
  border-color: theme('colors.indigo.700');
  color: white;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.finishButton:hover:not(.buttonDisabled) {
  background: linear-gradient(135deg, theme('colors.indigo.700'), theme('colors.indigo.800'));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.buttonDisabled {
  background: theme('colors.gray.300') !important;
  border-color: theme('colors.gray.300') !important;
  color: theme('colors.gray.500') !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
  opacity: 0.6;
}

:global(.dark) .buttonDisabled {
  background: theme('colors.gray.600') !important;
  border-color: theme('colors.gray.600') !important;
  color: theme('colors.gray.400') !important;
}

/* Question Card Styles */
.questionCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: theme('borderRadius.2xl');
  padding: theme('spacing.8');
  margin-bottom: theme('spacing.6');
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

:global(.dark) .questionCard {
  background: rgba(17, 24, 39, 0.95);
  border: 1px solid rgba(55, 65, 81, 0.3);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.questionHeader {
  margin-bottom: theme('spacing.6');
}

.questionRow {
  display: flex;
  align-items: flex-start;
  gap: theme('spacing.3');
  margin-bottom: theme('spacing.4');
}

.questionNumber {
  flex-shrink: 0;
  width: theme('spacing.10');
  height: theme('spacing.10');
  background: linear-gradient(135deg, theme('colors.blue.500'), theme('colors.indigo.600'));
  border-radius: theme('borderRadius.full');
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

:global(.dark) .questionNumber {
  background: linear-gradient(135deg, theme('colors.blue.600'), theme('colors.indigo.700'));
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.questionNumberText {
  color: white;
  font-weight: theme('fontWeight.bold');
  font-size: theme('fontSize.base');
}

.questionContent {
  flex-grow: 1;
}

.questionText {
  composes: bodyText from '@/styles/shared/components/typography.module.css';
  font-size: theme('fontSize.lg');
  font-weight: theme('fontWeight.medium');
  margin-bottom: theme('spacing.2');
  color: theme('colors.gray.900');
  line-height: theme('lineHeight.relaxed');
}

:global(.dark) .questionText {
  color: theme('colors.gray.100');
}

.exampleBox {
  background-color: theme('colors.gray.50');
  padding: theme('spacing.3');
  border-radius: theme('borderRadius.lg');
  margin-bottom: theme('spacing.4');
}

:global(.dark) .exampleBox {
  background-color: theme('colors.gray.800');
}

.exampleText {
  composes: mutedText from '@/styles/shared/components/typography.module.css';
  font-style: italic;
  color: theme('colors.gray.700');
}

:global(.dark) .exampleText {
  color: theme('colors.gray.300');
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.3');
}

/* Answer Option Base Styles using shared components */
.optionBase {
  composes: cardBase from '@/styles/shared/components/cards.module.css';
  padding: theme('spacing.5');
  border: 2px solid;
  cursor: pointer;
  border-radius: theme('borderRadius.xl');
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.optionBase::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.optionBase:hover::before {
  left: 100%;
}

:global(.dark) .optionBase {
  background: rgba(31, 41, 55, 0.8);
}

/* Option States - Normal (no feedback) */
.optionNormal {
  border-color: theme('colors.gray.300');
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.optionNormal:hover {
  border-color: theme('colors.blue.300');
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

:global(.dark) .optionNormal {
  border-color: theme('colors.gray.600');
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:global(.dark) .optionNormal:hover {
  border-color: theme('colors.blue.400');
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.optionSelected {
  border-color: theme('colors.blue.500');
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.2);
}

:global(.dark) .optionSelected {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(99, 102, 241, 0.2));
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

/* Feedback States (Practice Mode) */
.optionCorrect {
  border-color: theme('colors.green.500');
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(34, 197, 94, 0.2);
}

:global(.dark) .optionCorrect {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.2));
  box-shadow: 0 8px 16px rgba(34, 197, 94, 0.3);
}

.optionIncorrect {
  border-color: theme('colors.red.500');
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(239, 68, 68, 0.2);
}

:global(.dark) .optionIncorrect {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2));
  box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
}

.optionNeutral {
  border-color: theme('colors.gray.300');
  background: rgba(243, 244, 246, 0.5);
  opacity: 0.7;
}

:global(.dark) .optionNeutral {
  border-color: theme('colors.gray.600');
  background: rgba(75, 85, 99, 0.5);
}

/* Option Content */
.optionContainer {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
}

.optionCircle {
  width: theme('spacing.6');
  height: theme('spacing.6');
  border-radius: theme('borderRadius.full');
  border: 2px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
}

.optionCircleNormal {
  border-color: theme('colors.gray.300');
}

.optionCircleSelected {
  border-color: theme('colors.blue.500');
  background-color: theme('colors.blue.500');
  color: white;
}

.optionText {
  color: theme('colors.gray.900');
  flex-grow: 1;
}

:global(.dark) .optionText {
  color: white;
}

.optionCheckIcon {
  width: theme('spacing.5');
  height: theme('spacing.5');
  color: theme('colors.green.500');
  margin-left: auto;
}

.optionXIcon {
  width: theme('spacing.5');
  height: theme('spacing.5');
  color: theme('colors.red.500');
  margin-left: auto;
}

/* Feedback Styles */
.feedbackContainer {
  margin-top: theme('spacing.6');
  padding: theme('spacing.4');
  border-radius: theme('borderRadius.lg');
  border-left: 4px solid;
}

.feedbackCorrect {
  background-color: theme('colors.green.50');
  border-color: theme('colors.green.500');
}

:global(.dark) .feedbackCorrect {
  background-color: rgba(34, 197, 94, 0.1);
}

.feedbackIncorrect {
  background-color: theme('colors.red.50');
  border-color: theme('colors.red.500');
}

:global(.dark) .feedbackIncorrect {
  background-color: rgba(239, 68, 68, 0.1);
}

.feedbackContent {
  display: flex;
  align-items: flex-start;
  gap: theme('spacing.3');
}

.feedbackIcon {
  flex-shrink: 0;
  width: theme('spacing.6');
  height: theme('spacing.6');
  border-radius: theme('borderRadius.full');
  display: flex;
  align-items: center;
  justify-content: center;
}

.feedbackIconCorrect {
  background-color: theme('colors.green.500');
}

.feedbackIconIncorrect {
  background-color: theme('colors.red.500');
}

.feedbackIconSvg {
  width: theme('spacing.4');
  height: theme('spacing.4');
  color: white;
}

.feedbackTextContainer {
  flex-grow: 1;
}

.feedbackTitle {
  font-weight: theme('fontWeight.medium');
  margin-bottom: theme('spacing.2');
}

.feedbackTitleCorrect {
  color: theme('colors.green.800');
}

:global(.dark) .feedbackTitleCorrect {
  color: theme('colors.green.300');
}

.feedbackTitleIncorrect {
  color: theme('colors.red.800');
}

:global(.dark) .feedbackTitleIncorrect {
  color: theme('colors.red.300');
}

.feedbackExplanation {
  font-size: theme('fontSize.sm');
}

.feedbackExplanationCorrect {
  color: theme('colors.green.700');
}

:global(.dark) .feedbackExplanationCorrect {
  color: theme('colors.green.200');
}

.feedbackExplanationIncorrect {
  color: theme('colors.red.700');
}

:global(.dark) .feedbackExplanationIncorrect {
  color: theme('colors.red.200');
}

.feedbackCorrectAnswer {
  font-size: theme('fontSize.sm');
  color: theme('colors.red.700');
  margin-top: theme('spacing.2');
}

:global(.dark) .feedbackCorrectAnswer {
  color: theme('colors.red.300');
}

/* Navigation Styles */
.navigationContainer {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.6');
  justify-content: space-between;
  align-items: stretch;
  margin-top: theme('spacing.8');
}

@media (min-width: theme('screens.sm')) {
  .navigationContainer {
    flex-direction: row;
    align-items: center;
  }
}

.navigationLeft {
  display: flex;
  gap: theme('spacing.3');
  justify-content: flex-start;
  align-items: center;
}

.navigationRight {
  display: flex;
  gap: theme('spacing.3');
  justify-content: flex-end;
  align-items: center;
}

/* Question Overview (Mobile) */
.overviewContainer {
  margin-top: theme('spacing.6');
}

@media (min-width: theme('screens.sm')) {
  .overviewContainer {
    display: none;
  }
}

.overviewTitle {
  composes: sectionTitle from '@/styles/shared/components/typography.module.css';
  margin-bottom: theme('spacing.3');
}

.overviewGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: theme('spacing.2');
}

.questionButton {
  composes: buttonSecondary from '@/styles/shared/components/buttons.module.css';
  width: theme('spacing.8');
  height: theme('spacing.8');
  font-size: theme('fontSize.sm');
  border: 1px solid;
}

.questionButtonCurrent {
  background-color: theme('colors.blue.600');
  color: white;
  border-color: theme('colors.blue.600');
}

.questionButtonAnswered {
  background-color: theme('colors.green.100');
  color: theme('colors.green.800');
  border-color: theme('colors.green.300');
}

.questionButtonUnanswered {
  background-color: theme('colors.gray.100');
  color: theme('colors.gray.600');
  border-color: theme('colors.gray.300');
}

/* Mode Styles */
.modePractice::before {
  content: "🎯 ";
}

.modeTest::before {
  content: "📝 ";
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.optionBase {
  animation: fadeIn 0.3s ease-out;
}

.feedbackContainer {
  animation: slideIn 0.2s ease-out;
}

/* Helper Buttons (Hint/Explanation) */
.helperButtonsContainer {
  display: flex;
  gap: theme('spacing.4');
  margin-top: theme('spacing.8');
  margin-bottom: theme('spacing.8');
}

.helperButton {
  display: flex;
  align-items: center;
  padding: theme('spacing.2') theme('spacing.4');
  border-radius: theme('borderRadius.lg');
  border: 1px solid;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: theme('fontSize.sm');
  font-weight: theme('fontWeight.medium');
}

.helperButtonNormal {
  border-color: theme('colors.gray.300');
  color: theme('colors.gray.600');
  background: rgba(255, 255, 255, 0.8);
}

.helperButtonNormal:hover {
  background: rgba(249, 250, 251, 0.9);
}

:global(.dark) .helperButtonNormal {
  border-color: theme('colors.gray.600');
  color: theme('colors.gray.400');
  background: rgba(31, 41, 55, 0.8);
}

:global(.dark) .helperButtonNormal:hover {
  background: rgba(55, 65, 81, 0.9);
}

.helperButtonActive {
  border-color: theme('colors.yellow.300');
  color: theme('colors.yellow.700');
  background: rgba(254, 243, 199, 0.9);
}

:global(.dark) .helperButtonActive {
  border-color: theme('colors.yellow.700');
  color: theme('colors.yellow.300');
  background: rgba(217, 119, 6, 0.2);
}

.helperButtonActiveBlue {
  border-color: theme('colors.blue.300');
  color: theme('colors.blue.700');
  background: rgba(219, 234, 254, 0.9);
}

:global(.dark) .helperButtonActiveBlue {
  border-color: theme('colors.blue.700');
  color: theme('colors.blue.300');
  background: rgba(29, 78, 216, 0.2);
}

.helperIcon {
  width: theme('spacing.4');
  height: theme('spacing.4');
  margin-right: theme('spacing.2');
}

/* Content Boxes (Hint/Explanation) */
.contentBox {
  margin-bottom: theme('spacing.6');
  padding: theme('spacing.4');
  border-radius: theme('borderRadius.lg');
  border: 1px solid;
}

.hintBox {
  background: rgba(254, 243, 199, 0.9);
  border-color: theme('colors.yellow.200');
  backdrop-filter: blur(5px);
}

:global(.dark) .hintBox {
  background: rgba(217, 119, 6, 0.1);
  border-color: theme('colors.yellow.700');
}

.explanationBox {
  background: rgba(219, 234, 254, 0.9);
  border-color: theme('colors.blue.200');
  backdrop-filter: blur(5px);
}

:global(.dark) .explanationBox {
  background: rgba(29, 78, 216, 0.1);
  border-color: theme('colors.blue.700');
}

.contentBoxTitle {
  font-weight: theme('fontWeight.semibold');
  margin-bottom: theme('spacing.2');
}

.hintTitle {
  color: theme('colors.yellow.800');
}

:global(.dark) .hintTitle {
  color: theme('colors.yellow.300');
}

.explanationTitle {
  color: theme('colors.blue.800');
}

:global(.dark) .explanationTitle {
  color: theme('colors.blue.300');
}

.contentBoxText {
  color: theme('colors.yellow.700');
}

:global(.dark) .contentBoxText {
  color: theme('colors.yellow.300');
}

.explanationContent {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.3');
}

.explanationItem {
  color: theme('colors.blue.700');
}

:global(.dark) .explanationItem {
  color: theme('colors.blue.300');
}

.explanationItemTitle {
  font-weight: theme('fontWeight.medium');
  color: theme('colors.blue.800');
  margin-bottom: theme('spacing.1');
}

:global(.dark) .explanationItemTitle {
  color: theme('colors.blue.300');
}

.explanationItemText {
  font-size: theme('fontSize.sm');
}

.explanationItemRule {
  font-size: theme('fontSize.xs');
  color: theme('colors.blue.600');
  margin-top: theme('spacing.1');
  font-style: italic;
}

:global(.dark) .explanationItemRule {
  color: theme('colors.blue.400');
}

/* Option Circle Enhancement */
.optionCircleInner {
  width: theme('spacing.2');
  height: theme('spacing.2');
  background-color: white;
  border-radius: theme('borderRadius.full');
}

/* Responsive Design */
@media (max-width: theme('screens.sm')) {
  .headerTop {
    flex-direction: column;
    align-items: flex-start;
    gap: theme('spacing.2');
  }
  
  .navigationContainer {
    gap: theme('spacing.4');
    margin-top: theme('spacing.6');
    align-items: stretch;
  }
  
  .navigationLeft {
    justify-content: flex-start;
    width: 100%;
  }
  
  .navigationRight {
    justify-content: flex-end;
    width: 100%;
  }
  
  .navButton {
    min-width: theme('spacing.32');
    max-width: theme('spacing.48');
  }
  
  .questionRow {
    flex-direction: column;
    gap: theme('spacing.2');
  }
  
  .questionNumber {
    align-self: flex-start;
  }

  .helperButtonsContainer {
    flex-direction: column;
    gap: theme('spacing.2');
    margin-top: theme('spacing.6');
    margin-bottom: theme('spacing.6');
  }

  .helperButton {
    width: 100%;
    justify-content: center;
  }
}