import React, { useState } from 'react';
import { HelpCircle, Info, CheckCircle, X } from 'lucide-react';
import { PageWrapper } from '@/components/Layout/ResponsiveContainer';
import { useQuizScreenHandler } from './QuizScreen.handler';
import { useLanguage } from '@/context/LanguageContext';
import styles from './QuizScreen.module.css';
import { cn } from 'wf-shared/utils';

/**
 * QuizScreen Component
 * Pure UI component that renders the quiz interface
 * All business logic is handled by useQuizScreenHandler
 * Updated with new quiz UI design
 */
const QuizScreen: React.FC = () => {
  const { t } = useLanguage();
  const {
    // Core state
    quiz,
    questions,
    currentQuestionIndex,
    mode,
    loading,
    error,
    
    // UI state
    showFeedback,
    selectedAnswer,
    isAnswerSubmitted,
    
    // Computed properties
    currentQuestion,
    progress,
    
    // Handlers
    handleAnswerSelect,
    handleNextQuestion,
    handlePreviousQuestion,
    handleQuizComplete,
    handleQuit,
  } = useQuizScreenHandler();

  // Local state for hint and explanation visibility
  const [showHint, setShowHint] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);

  // Auto-expand explanation in practice mode when answer is selected
  React.useEffect(() => {
    if (mode === 'practice' && showFeedback && currentQuestion?.explanations?.length) {
      setShowExplanation(true);
      setShowHint(false); // Close hint if open
    }
  }, [showFeedback, mode, currentQuestion?.explanations?.length]);

  // Collapse explanation when moving to next question
  React.useEffect(() => {
    setShowExplanation(false);
    setShowHint(false);
  }, [currentQuestionIndex]);


  const handleHintClick = () => {
    setShowHint(!showHint);
    setShowExplanation(false);
  };

  const handleExplanationClick = () => {
    setShowExplanation(!showExplanation);
    setShowHint(false);
  };

  // Reset hint and explanation when question changes (now handled by useEffect)
  const resetHelpStates = () => {
    setShowHint(false);
    setShowExplanation(false);
  };

  // Wrapper functions to reset help states on navigation
  const handleNextQuestionWithReset = () => {
    resetHelpStates();
    handleNextQuestion();
  };

  const handlePreviousQuestionWithReset = () => {
    resetHelpStates();
    handlePreviousQuestion();
  };

  // Loading state - keep loading until we have quiz data and questions
  if (loading || (!quiz && !error)) {
    return (
      <PageWrapper>
        <div className={styles.wrapper}>
          <div className={styles.loadingWrapper}>
            <div className={styles.errorCard}>
              <div className={styles.loadingSpinner}></div>
              <p className={styles.loadingText}>{t('quiz.loadingQuiz')}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  // Error state
  if (error) {
    return (
      <PageWrapper>
        <div className={styles.wrapper}>
          <div className={styles.loadingWrapper}>
            <div className={styles.errorCard}>
              <div className={styles.errorIconContainer}>
                <X className={styles.errorIcon} />
              </div>
              <h2 className={styles.errorTitle}>{t('quiz.quizLoadingError')}</h2>
              <p className={styles.errorMessage}>{error}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  // No questions state
  if (!questions.length || !currentQuestion) {
    return (
      <PageWrapper>
        <div className={styles.wrapper}>
          <div className={styles.loadingWrapper}>
            <div className={styles.errorCard}>
              <h2 className={styles.errorTitle}>{t('quiz.noQuestionsAvailable')}</h2>
              <p className={styles.errorMessage}>{t('quiz.noQuestionsText')}</p>
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      {/* Quiz Header with Progress */}
      <div className={styles.header}>
        <div className={styles.headerTop}>
          <div className={styles.headerLeft}>
            <button
              onClick={handleQuit}
              className={styles.quitButton}
            >
              <X className="h-6 w-6" />
            </button>
            <div>
              <h1 className={cn(styles.title, mode === 'practice' ? styles.modePractice : styles.modeTest)}>
                {quiz?.title || 'Word Formation Quiz'}
              </h1>
              <p className={styles.subtitle}>
                {mode === 'practice' ? t('quiz.practiceMode') : t('quiz.testMode')}
              </p>
            </div>
          </div>
          <div className={styles.headerRight}>
            <div className={styles.questionInfo}>
              {t('quiz.question')} {currentQuestionIndex + 1} {t('quiz.of')} {questions.length}
            </div>
          </div>
        </div>

        {/* Improved Progress Bar */}
        <div>
          <div className={styles.answeredInfo}>
            <span>{t('quiz.progress')}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className={styles.progressBar}>
            <div 
              className={styles.progressFill}
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>

      <div className={styles.questionCard}>
        <div className={styles.questionHeader}>
          <div className={styles.questionRow}>
            <div className={styles.questionNumber}>
              <span className={styles.questionNumberText}>{currentQuestionIndex + 1}</span>
            </div>
            <div className={styles.questionContent}>
              <h2 className={styles.questionText}>
                {currentQuestion.question_text}
              </h2>
            </div>
          </div>
          {currentQuestion.metadata?.example_usage && (
            <div className={styles.exampleBox}>
              <p className={styles.exampleText}>
                <strong>{t('quiz.example')}:</strong> &ldquo;{currentQuestion.metadata.example_usage}&rdquo;
              </p>
            </div>
          )}
        </div>

        <div className={styles.optionsContainer}>
          {(currentQuestion.options || []).map((option) => {
            const optionText = option.option_text || '';
            const isSelected = selectedAnswer === optionText;
            const isCorrectOption = optionText === currentQuestion.correct_answer;
            const showAnswerFeedback = showFeedback && mode === 'practice';
            
            // Get option class based on state
            let optionClass = styles.optionBase;
            if (showAnswerFeedback) {
              if (isCorrectOption) {
                optionClass = cn(styles.optionBase, styles.optionCorrect);
              } else if (isSelected && !isCorrectOption) {
                optionClass = cn(styles.optionBase, styles.optionIncorrect);
              } else {
                optionClass = cn(styles.optionBase, styles.optionNeutral);
              }
            } else if (isSelected) {
              optionClass = cn(styles.optionBase, styles.optionSelected);
            } else {
              optionClass = cn(styles.optionBase, styles.optionNormal);
            }

            // Get circle class based on selection
            const circleClass = cn(
              styles.optionCircle,
              isSelected ? styles.optionCircleSelected : styles.optionCircleNormal
            );

            return (
              <button
                key={option.id}
                onClick={() => handleAnswerSelect(optionText)}
                className={optionClass}
                disabled={isAnswerSubmitted && mode === 'test'}
              >
                <div className={styles.optionContainer}>
                  <div className={circleClass}>
                    {isSelected && (
                      <div className={styles.optionCircleInner}></div>
                    )}
                  </div>
                  <span className={styles.optionText}>{optionText}</span>
                  {showAnswerFeedback && isCorrectOption && (
                    <CheckCircle className={styles.optionCheckIcon} />
                  )}
                  {showAnswerFeedback && isSelected && !isCorrectOption && (
                    <X className={styles.optionXIcon} />
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Hint and Explanation Buttons - Only in Practice Mode */}
        {mode === 'practice' && (
          <div className={styles.helperButtonsContainer}>
            {currentQuestion.metadata?.grammar_rule && (
              <button
                onClick={handleHintClick}
                className={cn(
                  styles.helperButton,
                  showHint ? styles.helperButtonActive : styles.helperButtonNormal
                )}
              >
                <HelpCircle className={styles.helperIcon} />
                {t('quiz.hint')}
              </button>
            )}
            {currentQuestion.explanations?.length && (
              <button
                onClick={handleExplanationClick}
                className={cn(
                  styles.helperButton,
                  showExplanation ? styles.helperButtonActiveBlue : styles.helperButtonNormal
                )}
              >
                <Info className={styles.helperIcon} />
                {t('quiz.explanation')}
              </button>
            )}
          </div>
        )}

        {/* Hint Content */}
        {showHint && currentQuestion.metadata?.grammar_rule && (
          <div className={cn(styles.contentBox, styles.hintBox)}>
            <h4 className={cn(styles.contentBoxTitle, styles.hintTitle)}>{t('quiz.hintTitle')}</h4>
            <p className={styles.contentBoxText}>{currentQuestion.metadata.grammar_rule}</p>
          </div>
        )}

        {/* Explanation Content */}
        {showExplanation && currentQuestion.explanations?.length && (
          <div className={cn(styles.contentBox, styles.explanationBox)}>
            <h4 className={cn(styles.contentBoxTitle, styles.explanationTitle)}>{t('quiz.explanationTitle')}</h4>

            {/* Show structured explanations */}
            <div className={styles.explanationContent}>
              {currentQuestion.explanations.map((explanation, index) => (
                <div key={explanation.id || index} className={styles.explanationItem}>
                  <div className={styles.explanationItemTitle}>
                    {explanation.explanation_type === 'correct_answer' ? t('quiz.correctAnswerLabel') : t('quiz.wrongAnswerLabel')}
                  </div>
                  <div className={styles.explanationItemText}>{explanation.content}</div>
                  {explanation.metadata?.rule && (
                    <div className={styles.explanationItemRule}>
                      {t('quiz.rule')}: {explanation.metadata.rule}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className={styles.navigationContainer}>
          <div className={styles.navigationLeft}>
            <button
              onClick={handlePreviousQuestionWithReset}
              disabled={currentQuestionIndex === 0}
              className={cn(
                styles.navButton,
                styles.previousButton,
                currentQuestionIndex === 0 && styles.buttonDisabled
              )}
            >
              {t('quiz.previous')}
            </button>
          </div>

          <div className={styles.navigationRight}>
            {currentQuestionIndex === questions.length - 1 ? (
              <button
                onClick={handleQuizComplete}
                disabled={!selectedAnswer}
                className={cn(
                  styles.navButton,
                  styles.finishButton,
                  !selectedAnswer && styles.buttonDisabled
                )}
              >
                {t('quiz.submitQuiz')}
              </button>
            ) : (
              <button
                onClick={handleNextQuestionWithReset}
                disabled={!selectedAnswer}
                className={cn(
                  styles.navButton,
                  styles.nextButton,
                  !selectedAnswer && styles.buttonDisabled
                )}
              >
                {t('quiz.next')}
              </button>
            )}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default QuizScreen; 