import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import type { 
  VocabularyDashboard, 
  SublistProgress
} from '@/types';

// Mock data structure for development
const mockDashboardData: VocabularyDashboard = {
  stats: {
    totalWordsStudied: 248,
    wordsMastered: 156,
    currentStreak: 12,
    bestStreak: 23,
    totalStudyTime: 8640, // seconds
    averageQuizScore: 87.3,
    totalQuizAttempts: 45,
    totalPoints: 1247,
    currentLevel: 3,
    awlCoverage: 43.5,
    favoriteStudyTime: "evening",
    mostProductiveDay: "tuesday"
  },
  sublistProgress: [
    { sublist: 1, totalWords: 60, wordsStudied: 58, averageScore: 89.2, completionPercentage: 96.7 },
    { sublist: 2, totalWords: 60, wordsStudied: 54, averageScore: 85.1, completionPercentage: 90.0 },
    { sublist: 3, totalWords: 60, wordsStudied: 45, averageScore: 82.3, completionPercentage: 75.0 },
    { sublist: 4, totalWords: 60, wordsStudied: 32, averageScore: 79.8, completionPercentage: 53.3 },
    { sublist: 5, totalWords: 60, wordsStudied: 28, averageScore: 77.2, completionPercentage: 46.7 },
    { sublist: 6, totalWords: 60, wordsStudied: 18, averageScore: 75.6, completionPercentage: 30.0 },
    { sublist: 7, totalWords: 60, wordsStudied: 8, averageScore: 73.1, completionPercentage: 13.3 },
    { sublist: 8, totalWords: 60, wordsStudied: 3, averageScore: 71.5, completionPercentage: 5.0 },
    { sublist: 9, totalWords: 60, wordsStudied: 1, averageScore: 70.0, completionPercentage: 1.7 },
    { sublist: 10, totalWords: 60, wordsStudied: 1, averageScore: 68.5, completionPercentage: 1.7 }
  ],
  recentWords: [],
  badges: [
    {
      id: 'badge_1',
      userId: 'user_1',
      badgeId: 'word_explorer',
      earnedAt: '2024-01-15T10:30:00Z',
      progressWhenEarned: {},
      instanceNumber: 1,
      badge: {
        id: 'word_explorer',
        badgeKey: 'word_explorer',
        name: 'Word Explorer',
        description: '50 words studied',
        iconName: 'BookOpen',
        color: 'blue',
        badgeCategory: 'milestone',
        requirementType: 'count',
        requirementValue: 50,
        requirementDescription: 'Study 50 words',
        tier: 'bronze',
        pointsAwarded: 100,
        isRepeatable: false,
        isHidden: false,
        sortOrder: 1,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      }
    },
    {
      id: 'badge_2',
      userId: 'user_1',
      badgeId: 'quiz_master',
      earnedAt: '2024-01-20T14:15:00Z',
      progressWhenEarned: {},
      instanceNumber: 1,
      badge: {
        id: 'quiz_master',
        badgeKey: 'quiz_master',
        name: 'Quiz Master',
        description: '25 quizzes completed',
        iconName: 'Trophy',
        color: 'gold',
        badgeCategory: 'milestone',
        requirementType: 'count',
        requirementValue: 25,
        requirementDescription: 'Complete 25 quizzes',
        tier: 'silver',
        pointsAwarded: 200,
        isRepeatable: false,
        isHidden: false,
        sortOrder: 2,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      }
    },
    {
      id: 'badge_3',
      userId: 'user_1',
      badgeId: 'week_warrior',
      earnedAt: '2024-01-25T09:45:00Z',
      progressWhenEarned: {},
      instanceNumber: 1,
      badge: {
        id: 'week_warrior',
        badgeKey: 'week_warrior',
        name: 'Week Warrior',
        description: '7-day study streak',
        iconName: 'Flame',
        color: 'orange',
        badgeCategory: 'streak',
        requirementType: 'streak',
        requirementValue: 7,
        requirementDescription: 'Study for 7 consecutive days',
        tier: 'bronze',
        pointsAwarded: 150,
        isRepeatable: true,
        isHidden: false,
        sortOrder: 3,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      }
    }
  ],
  availableBadges: [],
  streaks: [],
  recentSessions: [],
  recommendations: []
};

/**
 * Handler hook for VocabularyBuilderScreen component
 * Manages dashboard data fetching, progress calculations, and user interactions
 */
export const useVocabularyBuilderScreen = () => {
  const { user } = useAuth();
  const [error, setError] = useState<string | null>(null);

  // Query for dashboard data
  const {
    data: dashboardData,
    isLoading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ['vocabulary-dashboard', user?.id],
    queryFn: async (): Promise<VocabularyDashboard> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // TODO: Replace with actual API call to vocabulary service
      // const response = await vocabularyService.getDashboard(user.id);
      // return response.data;

      // For now, return mock data with a slight delay to simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return mockDashboardData;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  // Set error state from query error
  useEffect(() => {
    if (queryError) {
      setError(queryError instanceof Error ? queryError.message : 'Failed to load dashboard');
    } else {
      setError(null);
    }
  }, [queryError]);

  /**
   * Format study time from seconds to human-readable format
   */
  const formatStudyTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
  };

  /**
   * Calculate overall AWL progress percentage
   */
  const getProgressPercentage = (): number => {
    const totalAwlWords = 570; // Total AWL words
    const wordsStudied = dashboardData?.stats?.totalWordsStudied || 0;
    return Math.round((wordsStudied / totalAwlWords) * 100);
  };

  /**
   * Get sublist progress data
   */
  const getSublistProgress = (): SublistProgress[] => {
    return dashboardData?.sublistProgress || [];
  };

  /**
   * Calculate next milestone or recommendation
   */
  const getNextMilestone = () => {
    const stats = dashboardData?.stats;
    if (!stats) return null;

    const milestones = [
      { words: 50, name: "Word Explorer" },
      { words: 100, name: "Vocabulary Builder" },
      { words: 200, name: "Word Master" },
      { words: 350, name: "Language Scholar" },
      { words: 570, name: "AWL Champion" }
    ];

    const nextMilestone = milestones.find(m => m.words > stats.totalWordsStudied);
    if (nextMilestone) {
      const remaining = nextMilestone.words - stats.totalWordsStudied;
      return {
        name: nextMilestone.name,
        remaining,
        total: nextMilestone.words
      };
    }

    return null;
  };

  /**
   * Get learning recommendations based on progress
   */
  const getRecommendations = () => {
    const stats = dashboardData?.stats;
    if (!stats) return [];

    const recommendations = [];

    // Study streak recommendation
    if (stats.currentStreak === 0) {
      recommendations.push({
        type: 'start_streak',
        title: 'Start Your Learning Streak',
        description: 'Begin a daily study habit to improve retention',
        priority: 'high' as const
      });
    } else if (stats.currentStreak < 7) {
      recommendations.push({
        type: 'continue_streak',
        title: 'Keep Your Streak Going',
        description: `You're ${7 - stats.currentStreak} days away from the Week Warrior badge`,
        priority: 'medium' as const
      });
    }

    // Progress recommendation
    const nextMilestone = getNextMilestone();
    if (nextMilestone && nextMilestone.remaining <= 20) {
      recommendations.push({
        type: 'milestone',
        title: `Almost There: ${nextMilestone.name}`,
        description: `Study ${nextMilestone.remaining} more words to earn this achievement`,
        priority: 'high' as const
      });
    }

    // Quiz accuracy recommendation
    if (stats.averageQuizScore < 80) {
      recommendations.push({
        type: 'improve_accuracy',
        title: 'Improve Quiz Performance',
        description: 'Review previously studied words to boost your accuracy',
        priority: 'medium' as const
      });
    }

    return recommendations.slice(0, 3); // Limit to top 3 recommendations
  };

  /**
   * Refresh dashboard data
   */
  const refreshDashboard = () => {
    refetch();
  };

  return {
    // Data
    dashboardData,
    isLoading,
    error,
    
    // Computed values
    progressPercentage: getProgressPercentage(),
    sublistProgress: getSublistProgress(),
    nextMilestone: getNextMilestone(),
    recommendations: getRecommendations(),
    
    // Helper functions
    formatStudyTime,
    getProgressPercentage,
    getSublistProgress,
    
    // Actions
    refreshDashboard
  };
};