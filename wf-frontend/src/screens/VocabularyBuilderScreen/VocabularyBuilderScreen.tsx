import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, FileText, Lightbulb, TrendingUp, Trophy, Target, Clock, Star } from 'lucide-react';
import TopNavigation from '@/components/TopNavigation';
import { useVocabularyBuilderScreen } from './VocabularyBuilderScreen.handler';
import styles from './VocabularyBuilderScreen.module.css';

const VocabularyBuilderScreen: React.FC = () => {
  const {
    dashboardData,
    isLoading,
    error,
    formatStudyTime,
    getProgressPercentage,
    getSublistProgress
  } = useVocabularyBuilderScreen();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TopNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading your vocabulary dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TopNavigation />
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <h2 className="text-lg font-semibold text-red-900 mb-2">Unable to Load Dashboard</h2>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  const stats = dashboardData?.stats;
  const recentBadges = dashboardData?.badges?.slice(0, 3) || [];
  const sublistProgress = getSublistProgress();

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            📚 Your Learning Dashboard
          </h1>
          <p className="text-gray-600">
            Track your vocabulary progress and continue your learning journey
          </p>
        </div>

        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className={styles.statCard}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Words Studied</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.totalWordsStudied || 0}
                </p>
                <p className="text-xs text-gray-500">
                  {stats?.wordsMastered || 0} mastered
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Study Streak</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.currentStreak || 0} days
                </p>
                <p className="text-xs text-gray-500">
                  Best: {stats?.bestStreak || 0} days
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Quiz Accuracy</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.averageQuizScore ? Math.round(stats.averageQuizScore) : 0}%
                </p>
                <p className="text-xs text-gray-500">
                  {stats?.totalQuizAttempts || 0} attempts
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Study Time</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatStudyTime(stats?.totalStudyTime || 0)}
                </p>
                <p className="text-xs text-gray-500">
                  Total learning time
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Quick Actions */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
              <div className="space-y-3">
                <Link
                  to="/vocabulary-builder/awl-highlighter"
                  className={styles.actionButton}
                >
                  <FileText className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">AWL Highlighter</p>
                    <p className="text-sm text-gray-500">Analyze text for academic words</p>
                  </div>
                </Link>

                <Link
                  to="/vocabulary-builder/word-master"
                  className={styles.actionButton}
                >
                  <BookOpen className="h-5 w-5 text-green-600" />
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">Word Master</p>
                    <p className="text-sm text-gray-500">Search words and take quizzes</p>
                  </div>
                </Link>

                <Link
                  to="/assessment"
                  className={styles.actionButton}
                >
                  <Lightbulb className="h-5 w-5 text-purple-600" />
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">Practice Quizzes</p>
                    <p className="text-sm text-gray-500">Test your knowledge</p>
                  </div>
                </Link>
              </div>
            </div>

            {/* Recent Achievements */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                Recent Achievements
              </h2>
              {recentBadges.length > 0 ? (
                <div className="space-y-3">
                  {recentBadges.map((userBadge, index) => (
                    <div key={index} className="flex items-center p-3 bg-yellow-50 rounded-lg">
                      <div className="p-2 bg-yellow-100 rounded-full mr-3">
                        <Star className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{userBadge.badge?.name}</p>
                        <p className="text-sm text-gray-500">{userBadge.badge?.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">
                  Start learning to earn your first badges!
                </p>
              )}
            </div>
          </div>

          {/* Right Column - Progress Details */}
          <div className="lg:col-span-2">
            {/* Overall Progress */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">📈 Progress Overview</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Vocabulary Learning</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>AWL Words Progress</span>
                      <span>{getProgressPercentage()}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getProgressPercentage()}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500">
                      {stats?.totalWordsStudied || 0} of 570 AWL words studied
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Learning Activity</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Total Points:</span>
                      <span className="font-medium">{stats?.totalPoints || 0} 🏆</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Current Level:</span>
                      <span className="font-medium">Level {stats?.currentLevel || 1}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>AWL Coverage:</span>
                      <span className="font-medium">{stats?.awlCoverage || 0}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* AWL Sublist Progress */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">📚 AWL Sublist Progress</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((sublist) => {
                  const progress = sublistProgress.find(p => p.sublist === sublist);
                  const completionPercentage = progress?.completionPercentage || 0;
                  const wordsStudied = progress?.wordsStudied || 0;
                  const totalWords = progress?.totalWords || 60; // AWL sublists typically have ~60 words each

                  return (
                    <div key={sublist} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-900">Sublist {sublist}</h3>
                        <span className="text-sm text-gray-500">
                          {wordsStudied}/{totalWords}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${completionPercentage}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500">{completionPercentage}% complete</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VocabularyBuilderScreen;