/* VocabularyBuilderScreen CSS Module */

.statCard {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  transition: all 0.2s ease-in-out;
}

.statCard:hover {
  @apply shadow-md border-gray-300;
  transform: translateY(-1px);
}

.actionButton {
  @apply flex items-center p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all duration-200;
  text-decoration: none;
}

.actionButton:hover {
  transform: translateY(-1px);
}

.progressBar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progressFill {
  @apply h-2 rounded-full transition-all duration-300;
}

.progressFill.blue {
  @apply bg-blue-600;
}

.progressFill.green {
  @apply bg-green-600;
}

.progressFill.orange {
  @apply bg-orange-600;
}

.progressFill.purple {
  @apply bg-purple-600;
}

.badgeCard {
  @apply flex items-center p-3 rounded-lg;
  transition: all 0.2s ease-in-out;
}

.badgeCard.bronze {
  @apply bg-orange-50 border border-orange-200;
}

.badgeCard.silver {
  @apply bg-gray-50 border border-gray-300;
}

.badgeCard.gold {
  @apply bg-yellow-50 border border-yellow-200;
}

.badgeCard.platinum {
  @apply bg-purple-50 border border-purple-200;
}

.badgeIcon {
  @apply p-2 rounded-full mr-3;
}

.badgeIcon.bronze {
  @apply bg-orange-100 text-orange-600;
}

.badgeIcon.silver {
  @apply bg-gray-100 text-gray-600;
}

.badgeIcon.gold {
  @apply bg-yellow-100 text-yellow-600;
}

.badgeIcon.platinum {
  @apply bg-purple-100 text-purple-600;
}

.sublistCard {
  @apply border border-gray-200 rounded-lg p-4;
  transition: all 0.2s ease-in-out;
}

.sublistCard:hover {
  @apply border-gray-300 shadow-sm;
  transform: translateY(-1px);
}

.loadingSpinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

.errorContainer {
  @apply bg-red-50 border border-red-200 rounded-lg p-6 text-center;
}

.errorTitle {
  @apply text-lg font-semibold text-red-900 mb-2;
}

.errorMessage {
  @apply text-red-700;
}

.dashboardGrid {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-8;
}

.quickActionsColumn {
  @apply lg:col-span-1;
}

.progressColumn {
  @apply lg:col-span-2;
}

.statsGrid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.cardHeader {
  @apply flex items-center mb-4;
}

.cardTitle {
  @apply text-lg font-semibold text-gray-900;
}

.sectionIcon {
  @apply h-5 w-5 mr-2;
}

.emptyState {
  @apply text-gray-500 text-center py-4;
}

.recommendationCard {
  @apply p-4 border-l-4 rounded-lg;
}

.recommendationCard.high {
  @apply bg-red-50 border-red-400;
}

.recommendationCard.medium {
  @apply bg-yellow-50 border-yellow-400;
}

.recommendationCard.low {
  @apply bg-blue-50 border-blue-400;
}

.milestone {
  @apply flex items-center justify-between p-3 bg-blue-50 rounded-lg;
}

.milestoneProgress {
  @apply text-sm text-blue-700;
}

.milestoneBar {
  @apply w-full bg-blue-200 rounded-full h-2 mt-2;
}

.milestoneFill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .statsGrid {
    @apply grid-cols-1 sm:grid-cols-2;
  }
  
  .dashboardGrid {
    @apply grid-cols-1;
  }
  
  .actionButton {
    @apply p-3;
  }
  
  .statCard {
    @apply p-4;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .statCard {
    @apply bg-gray-800 border-gray-700;
  }
  
  .actionButton {
    @apply border-gray-700 hover:border-gray-600;
  }
  
  .sublistCard {
    @apply border-gray-700 hover:border-gray-600;
  }
}