# Word Formation Quiz Platform - Project Overview

## Purpose
A comprehensive language learning platform with user-facing frontend and admin content management portal for word formation exercises. Features interactive quiz system with multiple difficulty levels, progress tracking, and multi-language support (EN/VI/FR).

## Tech Stack
- **Frontend Framework**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + CSS Modules (shared patterns system)
- **State Management**: TanStack Query (React Query)
- **Backend**: Supabase (PostgreSQL + Auth + APIs)
- **UI Components**: Radix UI + Custom Components
- **Testing**: Vitest + React Testing Library
- **Icons**: Lucide React

## Project Architecture
```
WordFormation/
├── wf-frontend/          # User-facing React app (Port 5173)
├── wf-admin-portal/      # Admin content management (Port 3001)  
├── wf-shared/            # Shared types, utilities, services
├── database/             # SQL schema and sample data
├── documentation/        # Project documentation
└── .claude/             # Development standards and guidelines
```

## Key Features Completed
- Interactive quiz system with multiple modes (practice/test)
- User authentication & registration
- Progress tracking and analytics  
- Responsive design with dark mode
- Multi-language support
- Admin dashboard with content management
- Bundle isolation and security validation