# Database and Supabase Integration

## Database Schema Overview
Core tables in PostgreSQL via Supabase:
- **users**: User authentication and profiles
- **levels**: CEFR levels (A1, A2, B1, B2, C1, C2)
- **categories**: Quiz categories (grammar, vocabulary, reading, etc.)
- **quizzes**: Quiz metadata and configuration
- **questions**: Individual quiz questions
- **question_options**: Multiple choice options
- **quiz_attempts**: User quiz attempts and results
- **user_answers**: Individual question responses
- **user_progress**: Progress tracking data
- **badges**: Achievement system
- **explanations**: Question explanations and feedback

## MANDATORY: Supabase MCP Tools Usage
**NEVER use raw SQL commands via Bash - ALWAYS use MCP tools:**

### Schema Inspection
```bash
mcp__supabase__list_tables              # List all tables and schemas
mcp__supabase__list_extensions          # Show Postgres extensions
mcp__supabase__list_migrations          # View migration history
```

### Data Operations
```bash
mcp__supabase__execute_sql              # Run SELECT, INSERT, UPDATE, DELETE
mcp__supabase__apply_migration          # Apply DDL schema changes
mcp__supabase__generate_typescript_types # Generate types after changes
```

### Development & Debugging
```bash
mcp__supabase__get_logs                 # Check service logs
mcp__supabase__get_advisors             # Security/performance recommendations
mcp__supabase__get_project_url          # Get API URL
mcp__supabase__get_anon_key            # Get anonymous key
```

## Environment Configuration
```env
VITE_SUPABASE_URL=https://wumsrmqsqtdwgzmxwpjn.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Security Features
- Row-level security (RLS) enabled
- Role-based authentication  
- Input validation and sanitization
- Bundle isolation (admin code not exposed to users)