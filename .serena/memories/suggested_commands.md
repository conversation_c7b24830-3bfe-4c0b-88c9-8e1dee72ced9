# Suggested Commands for Development

## Daily Development Workflow

### Start Development Servers
```bash
# Frontend development (port 5173)
cd wf-frontend && npm run dev

# Admin portal development (port 3001)  
cd wf-admin-portal && npm run dev

# Shared package watch mode
cd wf-shared && npm run watch
```

### Code Quality Checks (MANDATORY BEFORE COMMITS)
```bash
# TypeScript compilation check
npx tsc --noEmit

# ESLint validation
npm run lint

# Run tests
npm test

# Test with coverage
npm run test:coverage
```

### Build Commands
```bash
# Build frontend for production
cd wf-frontend && npm run build

# Build admin portal for production
cd wf-admin-portal && npm run build

# Build shared package
cd wf-shared && npm run build
```

### Database Operations (USE SUPABASE MCP ONLY)
```bash
# Schema inspection
mcp__supabase__list_tables
mcp__supabase__list_migrations

# Data queries
mcp__supabase__execute_sql

# Schema changes
mcp__supabase__apply_migration
mcp__supabase__generate_typescript_types
```

### Git Workflow
```bash
# Check status
git status

# Stage changes
git add .

# Commit with proper format
git commit -m "type: description"

# Push changes  
git push origin branch-name
```

### System Utilities (Darwin/macOS)
```bash
ls -la                  # List files with details
find . -name "*.tsx"    # Find files by pattern
grep -r "search term"   # Search in files
cd path/to/directory    # Change directory
```