# Project Structure and Codebase Layout

## Top-Level Structure
```
WordFormation/
├── wf-frontend/                  # User-facing React application
│   ├── src/
│   │   ├── screens/             # Page components (3-file pattern)
│   │   ├── components/          # Reusable UI components
│   │   ├── services/            # API service layer
│   │   ├── repositories/        # Data access layer
│   │   ├── hooks/               # Custom React hooks
│   │   ├── types/               # TypeScript type definitions
│   │   ├── utils/               # Utility functions
│   │   ├── context/             # React context providers
│   │   ├── styles/              # CSS modules and shared styles
│   │   └── locales/             # Internationalization files
│   └── package.json             # Frontend dependencies
├── wf-admin-portal/              # Admin content management
│   ├── src/                     # Similar structure to frontend
│   └── package.json             # Admin portal dependencies
├── wf-shared/                    # Shared code between apps
│   ├── src/
│   │   ├── types/               # Shared TypeScript interfaces
│   │   ├── constants/           # Application constants
│   │   ├── utils/               # Shared utility functions
│   │   ├── repositories/        # Shared data access patterns
│   │   └── services/            # Shared business logic
│   └── package.json             # Shared package dependencies
├── database/                     # Database schema and migrations
│   ├── schema.sql               # Database structure
│   ├── migrations/              # Database migrations
│   └── sample-data/             # Test and seed data
├── documentation/                # Project documentation
│   ├── DEVELOPMENT_STANDARDS.md # Complete coding guidelines
│   ├── TECHNICAL_ARCHITECTURE.md # System design
│   └── CSS_ARCHITECTURE.md     # Styling patterns
└── .claude/                     # Development context for Claude AI
    ├── CODING_STANDARDS.md     # Detailed coding rules
    └── templates/              # Component templates
```

## Key Architectural Patterns
- **Hybrid Architecture**: Separate user and admin interfaces with shared core
- **Repository Pattern**: Clean separation of data access logic
- **Service Layer**: Business logic abstraction
- **CSS Modules**: Component-scoped styling with shared patterns
- **Type Safety**: Strict TypeScript across entire codebase