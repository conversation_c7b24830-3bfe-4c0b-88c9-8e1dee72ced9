# Coding Standards and Conventions

## Mandatory Development Workflow
**BEFORE ANY CODE WORK:**
1. ✅ **ALWAYS verify** TypeScript compilation with `npx tsc --noEmit`
2. ✅ **ALWAYS check** ESLint compliance with `npm run lint`  
3. ✅ **ALWAYS follow** 3-file pattern: `.tsx` (UI only) → `.handler.ts` (logic) → `.module.css` (optional)
4. ✅ **ALWAYS use** service layer - NO direct API calls in components

## Non-Negotiables (MUST STOP IF VIOLATED)
- ❌ **NEVER** use `any` types - Use proper TypeScript
- ❌ **NEVER** allow TypeScript compilation errors
- ❌ **NEVER** put business logic in .tsx files
- ❌ **NEVER** make direct API calls from components
- ✅ **ALWAYS** use handler hooks for state management
- ✅ **ALWAYS** follow 3-file pattern for components

## 3-File Component Pattern
```
ComponentName/
├── ComponentName.tsx          # Pure UI rendering only
├── ComponentName.handler.ts   # State management and logic  
├── ComponentName.module.css   # Component-specific styles (optional)
└── index.ts                   # Clean exports
```

## TypeScript Standards
- **Strict mode enabled** across all projects
- **Interface-driven development** for all data structures
- **Generic types** for reusable components
- **No `any` types** - Use proper interfaces

## Import Order Convention
```typescript
// 1. React imports
import React from 'react';
import { useState, useEffect } from 'react';

// 2. Third-party libraries
import { useQuery } from '@tanstack/react-query';

// 3. Internal imports (services, types, utils)
import { quizService } from '@/services';
import { Quiz } from 'wf-shared/types';

// 4. Relative imports
import styles from './Component.module.css';
```