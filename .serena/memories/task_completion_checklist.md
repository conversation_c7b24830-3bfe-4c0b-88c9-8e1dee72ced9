# Task Completion Checklist

## MANDATORY Steps Before Considering Any Task Complete

### 1. Code Quality Validation
```bash
npx tsc --noEmit                     # TypeScript compilation MUST pass
npm run lint                         # ESLint MUST pass with 0 warnings  
npm test                            # All tests MUST pass
```

### 2. Architecture Compliance
- [ ] Components follow 3-file pattern (.tsx → .handler.ts → .module.css)
- [ ] Business logic is in `.handler.ts` files ONLY
- [ ] No direct API calls in `.tsx` files - use service layer
- [ ] Proper import order followed
- [ ] No `any` types used - proper TypeScript interfaces

### 3. Database Operations (if applicable)
- [ ] Use Supabase MCP tools for all database work
- [ ] `mcp__supabase__list_tables` for schema inspection
- [ ] `mcp__supabase__execute_sql` for data queries
- [ ] `mcp__supabase__apply_migration` for schema changes
- [ ] `mcp__supabase__generate_typescript_types` after schema changes

### 4. Documentation Requirements
- [ ] Update relevant documentation in `/documentation/` folder
- [ ] No markdown files created in subproject directories
- [ ] Cross-reference related documents
- [ ] Update documentation index if new files added

### 5. Commit Standards (if committing)
```bash
# COMMIT MESSAGE FORMAT:
<type>: <short description>

<detailed explanation>
- What was changed
- Why it was changed
- Any migration phase context

🤖 Generated with Claude Code

Co-Authored-By: Claude <<EMAIL>>
```

### 6. Security & Performance
- [ ] Input validation on all forms
- [ ] No sensitive data exposed in logs or client code
- [ ] Bundle isolation maintained (no admin code in user builds)
- [ ] Proper error handling with user-friendly messages