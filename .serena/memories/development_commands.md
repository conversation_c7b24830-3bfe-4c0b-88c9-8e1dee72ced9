# Development Commands

## Essential Commands for Daily Development

### Frontend Development (wf-frontend)
```bash
cd wf-frontend && npm run dev        # Development server - http://localhost:5173
cd wf-frontend && npm run build      # Production build
cd wf-frontend && npm test           # Run tests
cd wf-frontend && npm run lint       # ESLint checking
```

### Admin Portal Development (wf-admin-portal)  
```bash
cd wf-admin-portal && npm run dev    # Development server - http://localhost:3001
cd wf-admin-portal && npm run build  # Production build
cd wf-admin-portal && npm test       # Run tests
cd wf-admin-portal && npm run lint   # ESLint checking
```

### Shared Package (wf-shared)
```bash
cd wf-shared && npm run build        # Build shared types and utilities
cd wf-shared && npm run watch        # Watch mode for development
cd wf-shared && npm test             # Run shared package tests
```

### Quality Assurance (MANDATORY BEFORE COMMITS)
```bash
npx tsc --noEmit                     # TypeScript compilation check
npm run lint                         # ESLint validation
npm test                            # Run all tests
```

### Darwin System Utilities
```bash
git                     # Version control (git version 2.39.5)
node                    # Node.js runtime (v24.4.1)
npm                     # Package manager
npx                     # Package runner
ls                      # List files
cd                      # Change directory
grep                    # Text search
find                    # File search
```