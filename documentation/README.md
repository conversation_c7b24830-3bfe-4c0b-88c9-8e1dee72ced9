# Documentation Index

> **📍 Single Source of Truth**: All project documentation is consolidated in this `documentation/` folder for easy maintenance and discovery.

## 📚 Core Architecture & Standards

### Primary Documentation
- **[DEVELOPMENT_STANDARDS.md](DEVELOPMENT_STANDARDS.md)** - 🎯 **SINGLE SOURCE OF TRUTH** for all coding standards, guidelines, and development practices
- **[TECHNICAL_ARCHITECTURE.md](TECHNICAL_ARCHITECTURE.md)** - System design, patterns, and architectural decisions
- **[CSS_ARCHITECTURE.md](CSS_ARCHITECTURE.md)** - CSS Modules system and styling standards

### Implementation Guides
- **[CSS_MODULES_IMPLEMENTATION.md](CSS_MODULES_IMPLEMENTATION.md)** - Detailed CSS Modules migration and usage guide
- **[SERVICE_ARCHITECTURE_PATTERNS.md](SERVICE_ARCHITECTURE_PATTERNS.md)** - Service layer patterns with dependency injection
- **[SERVICE_MIXINS_GUIDE.md](SERVICE_MIXINS_GUIDE.md)** - Service mixins and shared functionality patterns

### Feature Implementation Plans
- **[UNIFIED_LEARNING_DASHBOARD_IMPLEMENTATION_PLAN.md](UNIFIED_LEARNING_DASHBOARD_IMPLEMENTATION_PLAN.md)** - Comprehensive plan for unifying Progress and Vocabulary Builder screens

## 🛠️ Admin Portal Documentation

### User Guides
- **[ADMIN_BULK_IMPORT_GUIDE.md](ADMIN_BULK_IMPORT_GUIDE.md)** - Comprehensive guide for bulk importing quizzes, questions, and SQL data

## 🧪 Testing & Quality

### Testing Documentation
- **[TESTING_STRATEGY.md](TESTING_STRATEGY.md)** - Unit testing strategy, framework setup, and progress tracking

### Technical Debt & Maintenance
- **[TECHNICAL_DEBT_IMPLEMENTATION_GUIDE.md](TECHNICAL_DEBT_IMPLEMENTATION_GUIDE.md)** - Systematic approach to addressing technical debt issues

## 📦 Package Documentation

### Shared Package
- **[WF_SHARED_OVERVIEW.md](WF_SHARED_OVERVIEW.md)** - Overview of wf-shared package, security notices, and usage patterns

## 🏗️ Business & Project Management

### Business Documentation
- **[business-documentation.md](business-documentation.md)** - Business requirements, user stories, and project scope

### Migration & Planning
- **[../HYBRID_ARCHITECTURE_MIGRATION_PLAN.md](../HYBRID_ARCHITECTURE_MIGRATION_PLAN.md)** - Migration status and architectural evolution tracking

## 📋 Historical Archive

### Archived Documents
- **[archive/](archive/)** - Historical documents preserved for reference but no longer actively maintained

---

## 🎯 **Documentation Rules & Guidelines**

### ✅ **DO:**
- Create all new documentation in the `documentation/` folder
- Use descriptive, consistent naming conventions
- Cross-reference related documents
- Update this index when adding new files
- Include clear purpose and audience for each document

### ❌ **DON'T:**
- Create .md files in subproject directories (wf-frontend/, wf-admin-portal/, wf-shared/)
- Create duplicate documentation in multiple locations
- Use generic names like README.md in subdirectories
- Create documentation without linking it from this index

### 📝 **Naming Conventions:**
- `[COMPONENT]_[TYPE]_[PURPOSE].md` - e.g., `ADMIN_BULK_IMPORT_GUIDE.md`
- Use UPPERCASE for all documentation file names
- Be descriptive and specific

### 🔗 **Cross-Referencing:**
- Always use relative paths from documentation/ folder
- Link related documents in "See Also" sections  
- Reference this index from CLAUDE.md and other entry points

---

**Last Updated:** 2025-07-24  
**Total Documents:** 12 active + historical archive  
**Maintenance Status:** ✅ Actively maintained