# Vocabulary Builder Feature Implementation Plan

> Comprehensive plan for implementing the Vocabulary Builder feature with AWL Highlighter and Word Master tools

## 🎯 Feature Overview

The Vocabulary Builder is a new feature module that provides two powerful vocabulary learning tools:

1. **AWL Highlighter** - Text analysis tool that highlights Academic Word List (AWL) words in user input
2. **Word Master** - Interactive word search and learning tool with family trees and quizzes

## 📋 Requirements Analysis

### AWL Highlighter Requirements
- **Input**: Text area for user to paste paragraphs
- **Processing**: Analyze text and identify AWL words 
- **Highlighting**: Visual highlighting of AWL words in the text
- **Interaction**: Click on highlighted words to show definitions, examples, and meanings
- **Display**: Word information shown below or beside the text

### Word Master Requirements
- **Search**: Input box for word search functionality
- **Results Display**: Comprehensive word information including:
  - Word family snapshot with all forms (verb, noun, adjective, adverb)
  - IPA pronunciation
  - Vietnamese translations
  - Etymology and word-building notes
  - Interactive quiz (10 questions)
- **Quiz Interaction**: Fill-in-the-blank exercises with immediate feedback

## 🏗️ Architecture Design

### Navigation Integration
```
TopNavigation.tsx
├── Home
├── Assessment  
├── Progress
├── Vocabulary Builder (NEW) ◄── Add dropdown menu
    ├── AWL Highlighter
    └── Word Master
```

### Component Structure
Following the 3-file pattern for all components:

```
wf-frontend/src/screens/
├── VocabularyBuilderScreen/
│   ├── VocabularyBuilderScreen.tsx
│   ├── VocabularyBuilderScreen.handler.ts
│   ├── VocabularyBuilderScreen.module.css
│   └── index.ts
├── AwlHighlighterScreen/
│   ├── AwlHighlighterScreen.tsx
│   ├── AwlHighlighterScreen.handler.ts
│   ├── AwlHighlighterScreen.module.css
│   └── index.ts
└── WordMasterScreen/
    ├── WordMasterScreen.tsx
    ├── WordMasterScreen.handler.ts
    ├── WordMasterScreen.module.css
    └── index.ts
```

## 🗄️ Database Schema Design

### AWL Words Table
```sql
CREATE TABLE awl_words (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    word TEXT NOT NULL UNIQUE,
    sublists INTEGER NOT NULL CHECK (sublists BETWEEN 1 AND 10), -- AWL sublists 1-10
    frequency_rank INTEGER,
    word_forms JSONB DEFAULT '{}', -- {"verb": "analyze", "noun": "analysis", "adjective": "analytical", "adverb": "analytically"}
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE INDEX idx_awl_words_word ON awl_words(word);
CREATE INDEX idx_awl_words_sublists ON awl_words(sublists);
```

### Word Definitions Table
```sql
CREATE TABLE word_definitions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    awl_word_id UUID REFERENCES awl_words(id) ON DELETE CASCADE,
    word_form TEXT NOT NULL, -- "verb", "noun", "adjective", "adverb"
    word_text TEXT NOT NULL,
    ipa_pronunciation TEXT,
    vietnamese_meaning TEXT,
    english_definition TEXT,
    example_sentences JSONB DEFAULT '[]',
    etymology JSONB DEFAULT '{}', -- {"root": "Latin", "meaning": "to separate"}
    created_at TIMESTAMPTZ DEFAULT now()
);

CREATE INDEX idx_word_definitions_awl_word_id ON word_definitions(awl_word_id);
CREATE INDEX idx_word_definitions_word_text ON word_definitions(word_text);
```

### Word Building Notes Table
```sql
CREATE TABLE word_building_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    awl_word_id UUID REFERENCES awl_words(id) ON DELETE CASCADE,
    note_type TEXT NOT NULL, -- "prefix", "suffix", "root", "pattern"
    content TEXT NOT NULL,
    examples JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ DEFAULT now()
);
```

### Quiz Templates Table
```sql
CREATE TABLE vocabulary_quiz_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    awl_word_id UUID REFERENCES awl_words(id) ON DELETE CASCADE,
    question_template TEXT NOT NULL, -- "The company announced massive _____ due to automation. (noun, singular)"
    answer_form TEXT NOT NULL, -- "redundancy"
    word_form TEXT NOT NULL, -- "noun"
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
    created_at TIMESTAMPTZ DEFAULT now()
);
```

## 🔄 Service Layer Design

### VocabularyService
```typescript
export class VocabularyService extends BaseService {
  constructor(private repository: VocabularyRepository) {
    super();
  }

  async analyzeTextForAwlWords(text: string): Promise<ServiceResponse<AwlAnalysisResult>> {
    // Text analysis logic
  }

  async searchWord(word: string): Promise<ServiceResponse<WordDetails>> {
    // Word search logic
  }

  async generateQuiz(wordId: string): Promise<ServiceResponse<WordQuiz>> {
    // Quiz generation logic
  }
}
```

### VocabularyRepository
```typescript
export class VocabularyRepository extends BaseRepository {
  async findAwlWords(words: string[]): Promise<AwlWord[]> {
    // Database query for AWL words
  }

  async getWordDetails(word: string): Promise<WordDetails | null> {
    // Get complete word information
  }

  async getQuizQuestions(wordId: string): Promise<QuizQuestion[]> {
    // Get quiz questions for word
  }
}
```

## 🎨 UI/UX Design Specifications

### AWL Highlighter Interface
```
┌─────────────────────────────────────────────────────────────┐
│ AWL Highlighter                                             │
├─────────────────────────────────────────────────────────────┤
│ Paste your text here to analyze AWL words...               │
│                                                             │
│ [Large text area - 8-10 lines]                            │
│                                                             │
│                                      [Process Text] Button │
├─────────────────────────────────────────────────────────────┤
│ Analysis Results:                                           │
│                                                             │
│ The company announced massive redundancy due to            │
│ automation. Advanced [technology] has caused widespread    │
│ job [redundancy] across the [industry].                   │
│                                                             │
│ [Highlighted words are clickable]                          │
├─────────────────────────────────────────────────────────────┤
│ Word Details (when word clicked):                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Word: technology                                        │ │
│ │ IPA: /tekˈnɒlədʒi/                                     │ │
│ │ Meaning: công nghệ, kỹ thuật                           │ │
│ │ Example: Modern technology has revolutionized...        │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Word Master Interface
```
┌─────────────────────────────────────────────────────────────┐
│ Word Master                                                 │
├─────────────────────────────────────────────────────────────┤
│ Search Word: [_________________] [Search]                   │
├─────────────────────────────────────────────────────────────┤
│ ✅ Word: redundant                                         │
│                                                             │
│ ## 1️⃣ Word-Family Snapshot                                │
│                                                             │
│ | Part of Speech | Forms         | IPA           | Nghĩa   │
│ |----------------|---------------|---------------|---------|
│ | Verb           | — none —      | —             | —       │
│ | Noun           | redundancy    | /rɪˈdʌndənsi/ | sự dư.. │
│ | Adjective      | redundant     | /rɪˈdʌndənt/  | dư thừa │
│ | Adverb         | redundantly   | /rɪˈdʌnd../   | một..   │
│                                                             │
│ ## 2️⃣ Word-Building Notes                                 │
│ • Root: Latin redundare = "to overflow"                    │
│ • Suffixes: -ant → adjective, -ancy → noun, -ly → adverb  │
│                                                             │
│ ## 3️⃣ Interactive Quiz                [Start Quiz]        │
│ (Quiz appears below when started)                          │
└─────────────────────────────────────────────────────────────┘
```

### Quiz Interface
```
┌─────────────────────────────────────────────────────────────┐
│ Quiz: redundant (Question 3/10)                           │
├─────────────────────────────────────────────────────────────┤
│ Fill in the blank with the correct form:                   │
│                                                             │
│ "The company announced massive _______ due to automation." │
│ (noun, singular)                                           │
│                                                             │
│ Your answer: [________________]                            │
│                                                             │
│ [Submit Answer]  [Skip]  [Show Hint]                      │
│                                                             │
│ Progress: ██████████░░░░░░░░░░ 30%                        │
└─────────────────────────────────────────────────────────────┘
```

## 💻 Implementation Steps

### Phase 1: Database Setup
1. ✅ Create AWL words table with sample data
2. ✅ Create word definitions table
3. ✅ Create word building notes table  
4. ✅ Create quiz templates table
5. ✅ Populate with core AWL vocabulary data

### Phase 2: Service Layer
1. ✅ Implement VocabularyRepository with CRUD operations
2. ✅ Implement VocabularyService with business logic
3. ✅ Add text analysis algorithms for AWL detection
4. ✅ Add quiz generation logic

### Phase 3: Navigation Integration
1. ✅ Update TopNavigation component with dropdown menu
2. ✅ Add vocabulary builder menu item
3. ✅ Add sub-menu items for AWL Highlighter and Word Master
4. ✅ Update routing in App.tsx

### Phase 4: Screen Components
1. ✅ Create VocabularyBuilderScreen (landing page)
2. ✅ Create AwlHighlighterScreen with text analysis
3. ✅ Create WordMasterScreen with search and quiz
4. ✅ Implement all handlers following 3-file pattern

### Phase 5: UI Components
1. ✅ Create TextHighlighter component
2. ✅ Create WordDefinitionCard component  
3. ✅ Create InteractiveQuiz component
4. ✅ Create WordFamilyTable component

### Phase 6: Testing & Polish
1. ✅ Unit tests for service layer
2. ✅ Integration tests for components
3. ✅ Manual testing of user workflows
4. ✅ Performance optimization
5. ✅ Mobile responsiveness

## 🧪 Testing Strategy

### Unit Tests
- VocabularyService methods
- Text analysis algorithms
- Quiz generation logic
- Component handlers

### Integration Tests
- Full AWL highlighting workflow
- Complete word search and quiz workflow
- Navigation integration
- Database operations

### User Acceptance Tests
1. User can paste text and see AWL words highlighted
2. User can click highlighted words to see definitions
3. User can search for any word and see complete information
4. User can complete interactive quiz for any word
5. Navigation works correctly on desktop and mobile

## 📊 Data Requirements

### AWL Word List Data
- All 570 Academic Word List words
- Organized by sublists (1-10)
- Complete word family forms
- IPA pronunciations
- Vietnamese translations
- English definitions
- Example sentences

### Quiz Templates
- 10 questions per word minimum
- Various question types (fill-in-blank, multiple choice)
- Different difficulty levels
- Contextual examples

## 🔒 Security Considerations

1. **Input Validation**: Sanitize all text input to prevent XSS
2. **Rate Limiting**: Prevent abuse of text analysis API
3. **Data Privacy**: No storage of user-submitted text
4. **SQL Injection**: Use parameterized queries in repository layer

## 🚀 Performance Optimization

1. **Text Analysis**: Implement efficient string matching algorithms
2. **Caching**: Cache frequently searched words and definitions
3. **Lazy Loading**: Load quiz questions on demand
4. **Debouncing**: Debounce search input to reduce API calls
5. **Virtualization**: Use virtual scrolling for large word lists

## 📱 Mobile Considerations

1. **Responsive Design**: Ensure all components work on mobile devices
2. **Touch Interaction**: Optimize word clicking for touch screens
3. **Text Input**: Mobile-friendly text input and quiz interfaces
4. **Performance**: Optimize for mobile browsers and slower connections

## 🔄 Future Enhancements

1. **Advanced Analytics**: Track user learning progress
2. **Personalization**: Recommend words based on user level
3. **Offline Mode**: Cache data for offline usage
4. **Export Features**: Allow users to export word lists
5. **Social Features**: Share vocabulary learning progress

## ✅ Success Criteria

1. Users can successfully analyze text for AWL words
2. Word highlighting is visually clear and interactive
3. Word search returns comprehensive, accurate information
4. Quiz system is engaging and educational
5. Feature integrates seamlessly with existing navigation
6. All components follow project coding standards
7. Performance is acceptable on all target devices
8. Zero TypeScript compilation errors
9. All tests pass
10. ESLint compliance maintained

---

**Implementation Status**: Ready for development
**Estimated Effort**: 3-4 days development + 1 day testing  
**Dependencies**: Existing project architecture, Supabase database
**Risk Level**: Low (leverages existing patterns and architecture)