/**
 * Structured error handling utilities
 * Part of Technical Debt Solution - Phase 1, Task 1.1
 * 
 * Provides centralized error logging with environment-aware behavior,
 * replacing suppressed error handling throughout the codebase.
 */

import { generateUUID } from './ids';
import type { 
  ErrorContext, 
  ErrorSeverity, 
  ErrorLogEntry, 
  ErrorLoggerConfig, 
  ErrorStats 
} from '../types/errors';

/**
 * Default configuration for error logging
 */
const DEFAULT_CONFIG: ErrorLoggerConfig = {
  enableConsoleLogging: process.env.NODE_ENV === 'development',
  enableMonitoring: process.env.NODE_ENV === 'production',
  bufferSize: 100,
  flushInterval: 5000, // 5 seconds
};

/**
 * Centralized error logging system with structured context and severity levels
 */
export class ErrorLogger {
  private static config: ErrorLoggerConfig = { ...DEFAULT_CONFIG };
  private static errorBuffer: ErrorLogEntry[] = [];
  private static stats: ErrorStats = {
    totalErrors: 0,
    errorsBySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
    errorsByOperation: {},
    lastErrorTime: 0,
  };
  private static flushTimer: NodeJS.Timeout | null = null;

  /**
   * Resets the ErrorLogger to its initial state. Useful for testing.
   */
  static reset(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    this.config = { ...DEFAULT_CONFIG, enableConsoleLogging: false, enableMonitoring: false, flushInterval: 0 };
    this.errorBuffer = [];
    this.resetStats();
  }

  /**
   * Configure error logging behavior
   */
  static configure(config: Partial<ErrorLoggerConfig>): void {
    this.config = { ...this.config, ...config };
    this.setupFlushTimer();
  }

  /**
   * Log an error with structured context and severity
   */
  static log(
    error: Error, 
    context: ErrorContext, 
    severity: ErrorSeverity = 'medium'
  ): void {
    const entry = this.createLogEntry(error, context, severity);
    this.updateStats(entry);

    if (severity === 'critical') {
      this.logImmediate(entry);
    } else {
      this.bufferError(entry);
    }
  }

  /**
   * Log an error and return a fallback value
   */
  static logAndReturn<T>(
    error: Error,
    context: ErrorContext,
    fallback: T,
    severity: ErrorSeverity = 'medium'
  ): T {
    this.log(error, context, severity);
    return fallback;
  }

  /**
   * Get current error statistics
   */
  static getStats(): ErrorStats {
    return { ...this.stats };
  }

  /**
   * Reset error statistics
   */
  static resetStats(): void {
    this.stats = {
      totalErrors: 0,
      errorsBySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
      errorsByOperation: {},
      lastErrorTime: 0,
    };
  }

  /**
   * Flush all buffered errors immediately
   */
  static flush(): void {
    if (this.errorBuffer.length > 0) {
      this.sendBatchToMonitoring([...this.errorBuffer]);
      this.errorBuffer = [];
    }
  }

  private static createLogEntry(
    error: Error,
    context: ErrorContext,
    severity: ErrorSeverity
  ): ErrorLogEntry {
    return {
      error,
      context: {
        ...context,
        timestamp: context.timestamp || Date.now(),
      },
      severity,
      environment: process.env.NODE_ENV || 'development',
      id: generateUUID(),
    };
  }

  private static updateStats(entry: ErrorLogEntry): void {
    this.stats.totalErrors++;
    this.stats.errorsBySeverity[entry.severity]++;
    this.stats.errorsByOperation[entry.context.operation] = 
      (this.stats.errorsByOperation[entry.context.operation] || 0) + 1;
    this.stats.lastErrorTime = entry.context.timestamp || Date.now();
  }

  private static logImmediate(entry: ErrorLogEntry): void {
    if (this.config.enableConsoleLogging) {
      this.logToConsole(entry);
    }
    
    if (this.config.enableMonitoring) {
      this.sendToMonitoring(entry);
    }
  }

  private static bufferError(entry: ErrorLogEntry): void {
    if (this.config.enableConsoleLogging) {
      this.logToConsole(entry);
    }

    if (this.config.enableMonitoring) {
      this.errorBuffer.push(entry);
      
      if (this.errorBuffer.length >= this.config.bufferSize) {
        this.flush();
      }
    }
  }

  private static logToConsole(entry: ErrorLogEntry): void {
    const prefix = `[${entry.severity.toUpperCase()}] ${entry.context.operation}`;
    const metadata = entry.context.metadata 
      ? `\nMetadata: ${JSON.stringify(entry.context.metadata, null, 2)}`
      : '';
    
    // eslint-disable-next-line no-console
    console.error(`${prefix}:`, entry.error.message);
    // eslint-disable-next-line no-console
    console.error(`Stack: ${entry.error.stack}`);
    // eslint-disable-next-line no-console
    if (metadata) console.error(metadata);
    // eslint-disable-next-line no-console
    if (entry.context.userId) console.error(`User: ${entry.context.userId}`);
  }

  private static sendToMonitoring(entry: ErrorLogEntry): void {
    if (process.env.NODE_ENV === 'test') {
      const g = globalThis as typeof globalThis & { __errorLogEntries?: ErrorLogEntry[] };
      g.__errorLogEntries = g.__errorLogEntries || [];
      g.__errorLogEntries.push(entry);
    }
  }

  private static sendBatchToMonitoring(entries: ErrorLogEntry[]): void {
    entries.forEach(entry => this.sendToMonitoring(entry));
  }

  private static setupFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    if (this.config.enableMonitoring && this.config.flushInterval > 0) {
      this.flushTimer = setInterval(() => {
        this.flush();
      }, this.config.flushInterval);
    }
  }
}

ErrorLogger.configure({});