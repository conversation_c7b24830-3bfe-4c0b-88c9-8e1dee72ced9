import { describe, it, expect } from 'vitest';
import { isValidEmail, isStrongPassword } from '../validation';

describe('validation', () => {
  describe('isValidEmail', () => {
    it('should return true for a valid email address', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should return false for an invalid email address', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('invalid@')).toBe(false);
      expect(isValidEmail('@domain.com')).toBe(false);
      expect(isValidEmail('test@.com')).toBe(false);
      expect(isValidEmail('test@domain')).toBe(false);
      expect(isValidEmail('')).toBe(false);
      expect(isValidEmail(null as unknown as string)).toBe(false);
      expect(isValidEmail(undefined as unknown as string)).toBe(false);
    });
  });

  describe('isStrongPassword', () => {
    it('should return true for a strong password', () => {
      expect(isStrongPassword('StrongP@ssw0rd')).toBe(true);
      expect(isStrongPassword('Another_P@ssw0rd123')).toBe(true);
      expect(isStrongPassword('P@ssw0rd1234567890')).toBe(true);
    });

    it('should return false for a password that is too short', () => {
      expect(isStrongPassword('Short1@')).toBe(false);
      expect(isStrongPassword('1234567')).toBe(false);
    });

    it('should return false for a password missing an uppercase letter', () => {
      expect(isStrongPassword('strongp@ssw0rd')).toBe(false);
    });

    it('should return false for a password missing a lowercase letter', () => {
      expect(isStrongPassword('STRONGP@SSW0RD')).toBe(false);
    });

    it('should return false for a password missing a number', () => {
      expect(isStrongPassword('StrongP@ssword')).toBe(false);
    });

    it('should return false for a password missing a special character', () => {
      expect(isStrongPassword('StrongP4ssw0rd')).toBe(false);
    });

    it('should return false for an empty password', () => {
      expect(isStrongPassword('')).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isStrongPassword(null as unknown as string)).toBe(false);
      expect(isStrongPassword(undefined as unknown as string)).toBe(false);
    });
  });
});
