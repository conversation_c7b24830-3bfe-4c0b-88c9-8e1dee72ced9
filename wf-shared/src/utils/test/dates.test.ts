import { describe, it, expect, afterEach, beforeEach, vi } from 'vitest';
import {
  getStartOfWeek,
  getEndOfWeek,
  getStartOfMonth,
  getEndOfMonth,
  isSameDay,
  isToday,
  daysBetween,
  addDays,
  subtractDays,
  getRelativeTime,
} from '../dates';

describe('Date Utilities', () => {
  // Mock the current date to ensure tests are consistent
  const mockDate = new Date('2025-07-23T12:00:00.000Z');

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('getStartOfWeek', () => {
    it('should return the start of the week (Monday) for a given date', () => {
      const startOfWeek = getStartOfWeek(mockDate);
      expect(startOfWeek.toISOString()).toBe('2025-07-21T00:00:00.000Z');
    });
  });

  describe('getEndOfWeek', () => {
    it('should return the end of the week (Sunday) for a given date', () => {
      const endOfWeek = getEndOfWeek(mockDate);
      expect(endOfWeek.toISOString()).toBe('2025-07-27T23:59:59.999Z');
    });
  });

  describe('getStartOfMonth', () => {
    it('should return the start of the month for a given date', () => {
      const startOfMonth = getStartOfMonth(mockDate);
      expect(startOfMonth.toISOString()).toBe('2025-07-01T00:00:00.000Z');
    });
  });

  describe('getEndOfMonth', () => {
    it('should return the end of the month for a given date', () => {
      const endOfMonth = getEndOfMonth(mockDate);
      expect(endOfMonth.toISOString()).toBe('2025-07-31T23:59:59.999Z');
    });
  });

  describe('isSameDay', () => {
    it('should return true if two dates are on the same day', () => {
      const date1 = new Date('2025-07-23T10:00:00.000Z');
      const date2 = new Date('2025-07-23T20:00:00.000Z');
      expect(isSameDay(date1, date2)).toBe(true);
    });

    it('should return false if two dates are on different days', () => {
      const date1 = new Date('2025-07-22T10:00:00.000Z');
      const date2 = new Date('2025-07-23T20:00:00.000Z');
      expect(isSameDay(date1, date2)).toBe(false);
    });
  });

  describe('isToday', () => {
    it('should return true if the date is today', () => {
      expect(isToday(mockDate)).toBe(true);
    });

    it('should return false if the date is not today', () => {
      const anotherDate = new Date('2025-07-24T12:00:00.000Z');
      expect(isToday(anotherDate)).toBe(false);
    });
  });

  describe('daysBetween', () => {
    it('should calculate the number of days between two dates', () => {
      const date1 = new Date('2025-07-20T12:00:00.000Z');
      const date2 = new Date('2025-07-25T12:00:00.000Z');
      expect(daysBetween(date1, date2)).toBe(5);
    });
  });

  describe('addDays', () => {
    it('should add days to a date', () => {
      const newDate = addDays(mockDate, 5);
      expect(newDate.toISOString()).toBe('2025-07-28T12:00:00.000Z');
    });
  });

  describe('subtractDays', () => {
    it('should subtract days from a date', () => {
      const newDate = subtractDays(mockDate, 5);
      expect(newDate.toISOString()).toBe('2025-07-18T12:00:00.000Z');
    });
  });

  describe('getRelativeTime', () => {
    it('should return "just now" for a very recent time', () => {
      const recentDate = new Date(mockDate.getTime() - 10000); // 10 seconds ago
      expect(getRelativeTime(recentDate, mockDate)).toBe('just now');
    });

    it('should return minutes ago', () => {
      const fewMinutesAgo = new Date(mockDate.getTime() - 5 * 60 * 1000); // 5 minutes ago
      expect(getRelativeTime(fewMinutesAgo, mockDate)).toBe('5m ago');
    });

    it('should return hours ago', () => {
      const fewHoursAgo = new Date(mockDate.getTime() - 3 * 60 * 60 * 1000); // 3 hours ago
      expect(getRelativeTime(fewHoursAgo, mockDate)).toBe('3h ago');
    });

    it('should return days ago', () => {
      const fewDaysAgo = new Date(mockDate.getTime() - 4 * 24 * 60 * 60 * 1000); // 4 days ago
      expect(getRelativeTime(fewDaysAgo, mockDate)).toBe('4d ago');
    });

    it('should return the full date for more than 7 days ago', () => {
      const moreThanAWeekAgo = new Date(mockDate.getTime() - 8 * 24 * 60 * 60 * 1000); // 8 days ago
      expect(getRelativeTime(moreThanAWeekAgo, mockDate)).toBe(moreThanAWeekAgo.toLocaleDateString());
    });
  });
});
