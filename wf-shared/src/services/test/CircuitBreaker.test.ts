import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { CircuitBreaker, CircuitBreakerFactory } from '../CircuitBreaker';


// Mock the ErrorLogger to prevent actual logging during tests
vi.mock('../../utils/errorHandling', () => ({
  ErrorLogger: {
    log: vi.fn(),
  },
}));

// Mock timers to control time-based logic (setTimeout)
vi.useFakeTimers();

describe('CircuitBreaker', () => {
  let circuitBreaker: CircuitBreaker;

  beforeEach(() => {
    // Initialize a new circuit breaker before each test
    circuitBreaker = new CircuitBreaker({
      failureThreshold: 3,
      resetTimeout: 1000, // 1 second
      timeoutMs: 500, // 0.5 seconds
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Restore real timers after each test
    vi.restoreAllMocks();
  });

  describe('State Transitions', () => {
    it('should start in CLOSED state', () => {
      expect(circuitBreaker.getStats().state).toBe('CLOSED');
    });

    it('should transition to OPEN state after reaching failure threshold', async () => {
      const failingOperation = () => Promise.reject(new Error('Operation failed'));

      // Fail 3 times to trip the breaker
      for (let i = 0; i < 3; i++) {
        await circuitBreaker.execute(failingOperation);
      }

      expect(circuitBreaker.getStats().state).toBe('OPEN');
    });

    it('should transition to HALF_OPEN state after reset timeout', async () => {
      // Trip the breaker
      const failingOperation = () => Promise.reject(new Error('Operation failed'));
      for (let i = 0; i < 3; i++) {
        await circuitBreaker.execute(failingOperation);
      }

      // Advance time past the reset timeout
      vi.advanceTimersByTime(1001);

      // The next request should put it in HALF_OPEN
      const succeedingOperation = () => Promise.resolve('Success');
      await circuitBreaker.execute(succeedingOperation);

      expect(circuitBreaker.getStats().state).toBe('HALF_OPEN');
    });

    it('should transition back to OPEN if a call fails in HALF_OPEN state', async () => {
      // Trip the breaker and move to HALF_OPEN
      const failingOperation = () => Promise.reject(new Error('Operation failed'));
      for (let i = 0; i < 3; i++) {
        await circuitBreaker.execute(failingOperation);
      }
      vi.advanceTimersByTime(1001);
      await circuitBreaker.execute(failingOperation); // Moves to HALF_OPEN

      // Fail again in HALF_OPEN
      await circuitBreaker.execute(failingOperation);

      expect(circuitBreaker.getStats().state).toBe('OPEN');
    });

    it('should transition to CLOSED if calls succeed in HALF_OPEN state', async () => {
      const failingOperation = () => Promise.reject(new Error('Operation failed'));
      const succeedingOperation = () => Promise.resolve('Success');

      // Trip the breaker and move to HALF_OPEN
      for (let i = 0; i < 3; i++) {
        await circuitBreaker.execute(failingOperation);
      }
      vi.advanceTimersByTime(1001);
      await circuitBreaker.execute(succeedingOperation); // Moves to HALF_OPEN

      // Succeed enough times to close the circuit
      for (let i = 0; i < 3; i++) {
        await circuitBreaker.execute(succeedingOperation);
      }

      expect(circuitBreaker.getStats().state).toBe('CLOSED');
    });
  });

  describe('Execution Logic', () => {
    it('should execute the operation when the circuit is CLOSED', async () => {
      const operation = vi.fn().mockResolvedValue('Success');
      const result = await circuitBreaker.execute(operation);

      expect(operation).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.data).toBe('Success');
    });

    it('should reject the operation when the circuit is OPEN', async () => {
      // Trip the breaker
      const failingOperation = () => Promise.reject(new Error('Operation failed'));
      for (let i = 0; i < 3; i++) {
        await circuitBreaker.execute(failingOperation);
      }

      const operation = vi.fn();
      const result = await circuitBreaker.execute(operation);

      expect(operation).not.toHaveBeenCalled();
      expect(result.success).toBe(false);
      expect(result.rejected).toBe(true);
    });

    it('should handle timeouts correctly', async () => {
      const slowOperation = () => new Promise(resolve => setTimeout(() => resolve('done'), 1000));
      const promise = circuitBreaker.execute(slowOperation);

      // Advance timers to trigger the timeout
      vi.advanceTimersByTime(1001);

      const result = await promise;

      expect(result.success).toBe(false);
      expect(result.error).toContain('timed out');
      expect(circuitBreaker.getStats().state).toBe('CLOSED'); // Should not open on a single timeout
    });
  });

  describe('Event Emission', () => {
    it('should emit a STATE_CHANGE event when the state changes', () => {
      const listener = vi.fn();
      circuitBreaker.on('STATE_CHANGE', listener);

      circuitBreaker.forceState('OPEN');

      expect(listener).toHaveBeenCalledWith(expect.objectContaining({ type: 'STATE_CHANGE' }));
    });
  });

  describe('CircuitBreakerFactory', () => {
    beforeEach(() => {
      CircuitBreakerFactory.clearInstances();
    });

    it('should create and return a new instance', () => {
      const instance = CircuitBreakerFactory.getInstance('test');
      expect(instance).toBeInstanceOf(CircuitBreaker);
    });

    it('should return the same instance for the same name', () => {
      const instance1 = CircuitBreakerFactory.getInstance('test');
      const instance2 = CircuitBreakerFactory.getInstance('test');
      expect(instance1).toBe(instance2);
    });
  });
});
