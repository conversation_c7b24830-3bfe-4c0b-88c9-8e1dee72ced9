import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { HealthMonitor } from '../HealthMonitor';


// Mock external dependencies at the module level
vi.mock('../../repositories/DatabaseProvider');
vi.mock('../CacheManager');
vi.mock('../CircuitBreaker');

describe('HealthMonitor', () => {
  let healthMonitor: HealthMonitor;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.resetModules(); // Resets the module cache, so HealthMonitor is re-imported fresh

    // Dynamically import HealthMonitor after resetting modules
    const HealthMonitorModule = await import('../HealthMonitor');
    healthMonitor = HealthMonitorModule.HealthMonitor.getInstance();

    // Mock the checkServiceHealth method to control service health directly
    vi.spyOn(healthMonitor as unknown as { checkServiceHealth: (serviceName: string) => Promise<{ status: string; message: string; lastCheckTime: number }> }, 'checkServiceHealth').mockImplementation(async (serviceName: string) => {
      switch (serviceName) {
        case 'database':
        case 'auth':
        case 'cache':
        case 'circuit-breaker':
          return { status: 'up', message: `${serviceName} is up`, lastCheckTime: Date.now() };
        default:
          return { status: 'up', message: 'Unknown service is up', lastCheckTime: Date.now() };
      }
    });
  });

  afterEach(() => {
    healthMonitor.stopMonitoring();
    vi.restoreAllMocks(); // Restore spies after each test
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = HealthMonitor.getInstance();
      const instance2 = HealthMonitor.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Overall Health Check', () => {
    it('should return healthy if all services are up', async () => {
      const health = await healthMonitor.getOverallHealth();
      expect(health.overall).toBe('healthy');
    });

    it('should return unhealthy if any service is down', async () => {
      // Mock a specific service to be down for this test
      vi.spyOn(healthMonitor as unknown as { checkServiceHealth: (serviceName: string) => Promise<{ status: string; message: string; lastCheckTime: number }> }, 'checkServiceHealth').mockImplementation(async (serviceName: string) => {
        if (serviceName === 'database') {
          return { status: 'down', message: 'DB down', lastCheckTime: Date.now() };
        }
        return { status: 'up', message: `${serviceName} is up`, lastCheckTime: Date.now() };
      });

      const health = await healthMonitor.getOverallHealth();
      expect(health.overall).toBe('unhealthy');
    });
  });

  describe('Monitoring', () => {
    it('should start and stop monitoring', () => {
      healthMonitor.startMonitoring();
      // @ts-ignore
      expect(healthMonitor.isMonitoring).toBe(true);
      healthMonitor.stopMonitoring();
      // @ts-ignore
      expect(healthMonitor.isMonitoring).toBe(false);
    });
  });
});