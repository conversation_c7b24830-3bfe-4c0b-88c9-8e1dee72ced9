/**
 * Base API repository implementation for database operations.
 * Provides common CRUD functionality for all API repositories.
 * 
 * This abstract class implements the repository pattern for Supabase database operations,
 * offering standardized methods for data access with consistent error handling and type safety.
 * 
 * @template T - The entity type this repository manages
 * @template TCreate - The type for creating new entities (defaults to Partial<T>)
 * @template TUpdate - The type for updating entities (defaults to Partial<T>)
 * 
 * @example
 * ```typescript
 * class UserRepository extends BaseApiRepository<User, CreateUserData, UpdateUserData> {
 *   protected tableName = 'users' as const
 *   
 *   protected applyFilters(query: any, filters: Record<string, any>) {
 *     if (filters.role) query = query.eq('role', filters.role)
 *     return query
 *   }
 * }
 * ```
 */

import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database, PaginationParams, ApiResponse } from '../../types'
import type { IRepository } from '../interfaces/IRepository'
import { DatabaseProvider } from '../DatabaseProvider'
import { ErrorLogger } from '../../utils/errorHandling'
import { CircuitBreakerFactory } from '../../services/CircuitBreaker'

export abstract class BaseApiRepository<T, TCreate = Partial<T>, TUpdate = Partial<T>> 
  implements IRepository<T, TCreate, TUpdate> {
  
  /**
   * The Supabase client instance used for database operations
   */
  protected db: SupabaseClient<Database>
  
  /**
   * The database table name this repository operates on.
   * Must be implemented by concrete repository classes.
   */
  protected abstract tableName: keyof Database['public']['Tables']

  /**
   * Circuit breaker for read operations (queries, selects)
   */
  protected readCircuitBreaker = CircuitBreakerFactory.getInstance('database-read', 'DATABASE');

  /**
   * Circuit breaker for write operations (insert, update, delete)
   */
  protected writeCircuitBreaker = CircuitBreakerFactory.getInstance('database-write', 'DATABASE');

  /**
   * Creates a new BaseApiRepository instance.
   * 
   * @param database - Optional Supabase client instance. If not provided,
   *                   will use the default client from DatabaseProvider.
   */
  constructor(database?: SupabaseClient<Database>) {
    // Use provided database or fall back to default
    if (database) {
      this.db = database
    } else {
      this.db = DatabaseProvider.getInstance().getDefaultClient()
    }
    
    // Setup circuit breaker event listeners
    this.setupCircuitBreakerEvents();
  }
  
  /**
   * Setup event listeners for circuit breaker state changes
   */
  private setupCircuitBreakerEvents(): void {
    this.readCircuitBreaker.on('STATE_CHANGE', (event) => {
      ErrorLogger.log(
        new Error(`Database read circuit breaker: ${event.previousState} -> ${event.state}`),
        {
          operation: 'circuit-breaker-state-change',
          metadata: {
            circuitName: event.circuitName,
            tableName: this.tableName,
            previousState: event.previousState,
            newState: event.state
          }
        },
        event.state === 'OPEN' ? 'high' : 'medium'
      );
    });
    
    this.writeCircuitBreaker.on('STATE_CHANGE', (event) => {
      ErrorLogger.log(
        new Error(`Database write circuit breaker: ${event.previousState} -> ${event.state}`),
        {
          operation: 'circuit-breaker-state-change',
          metadata: {
            circuitName: event.circuitName,
            tableName: this.tableName,
            previousState: event.previousState,
            newState: event.state
          }
        },
        event.state === 'OPEN' ? 'critical' : 'high'
      );
    });
  }

  /**
   * Retrieves all items from the repository with optional pagination and filtering.
   * 
   * @param params - Optional parameters for pagination and filtering
   * @param params.page - Page number (1-based, defaults to 1)
   * @param params.pageSize - Number of items per page (defaults to 10)
   * @param params.filters - Additional filter parameters passed to applyFilters method
   * @returns Promise resolving to paginated results with metadata
   * 
   * @example
   * ```typescript
   * const result = await repository.getAll({ page: 2, pageSize: 20, status: 'active' })
   * if (result.success) {
   *   console.log('Items:', result.data.data)
   *   console.log('Total:', result.data.total)
   * }
   * ```
   */
  async getAll(params?: PaginationParams & Record<string, unknown>): Promise<ApiResponse<{
    data: T[]
    total: number
    page: number
    pageSize: number
  }>> {
    try {
      const { page = 1, pageSize = 10, ...filters } = params || {}
      const offset = (page - 1) * pageSize

      // Execute database query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        let query = this.db.from(this.tableName).select('*', { count: 'exact' })

        // Apply filters
        query = this.applyFilters(query, filters)

        // Apply pagination
        query = query.range(offset, offset + pageSize - 1)

        return await query
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Database operation failed', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data, error, count } = result.data!;

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: {
          data: data as T[],
          total: count || 0,
          page,
          pageSize
        }
      }
    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error('Failed to fetch data'),
        {
          operation: 'repository-find-many',
          metadata: {
            tableName: this.tableName,
            filters: params?.filters,
            pagination: { page: params?.page, pageSize: params?.pageSize }
          }
        },
        'medium'
      );
      return {
        success: false,
        error: { message: 'Failed to fetch data', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Retrieves a single item by its unique identifier.
   * 
   * @param id - The unique identifier of the item to retrieve
   * @returns Promise resolving to the item if found, or error if not found
   * 
   * @example
   * ```typescript
   * const result = await repository.getById('123e4567-e89b-12d3-a456-426614174000')
   * if (result.success) {
   *   console.log('Item:', result.data)
   * } else {
   *   console.error('Error:', result.error.message)
   * }
   * ```
   */
  async getById(id: string): Promise<ApiResponse<T>> {
    try {
      // Execute query through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select('*')
          .eq('id', id)
          .single()
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Database operation failed', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data, error } = result.data!;

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: data as T
      }
    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error('Failed to fetch item'),
        {
          operation: 'repository-find-by-id',
          metadata: {
            tableName: this.tableName,
            id
          }
        },
        'medium'
      );
      return {
        success: false,
        error: { message: 'Failed to fetch item', code: 'FETCH_ERROR' }
      }
    }
  }

  /**
   * Creates a new item in the repository.
   * 
   * @param data - The data for creating the new item
   * @returns Promise resolving to the created item with generated fields
   * 
   * @example
   * ```typescript
   * const result = await repository.create({ name: 'John Doe', email: '<EMAIL>' })
   * if (result.success) {
   *   console.log('Created item:', result.data)
   * }
   * ```
   */
  async create(data: TCreate): Promise<ApiResponse<T>> {
    try {
      // Execute database write through circuit breaker
      const result = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .insert(data as Record<string, unknown>)
          .select()
          .single()
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Database write operation failed', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: dbResult, error } = result.data!;

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: dbResult as T
      }
    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error('Failed to create item'),
        {
          operation: 'repository-create',
          metadata: {
            tableName: this.tableName,
            dataKeys: Object.keys(data || {})
          }
        },
        'high'
      );
      return {
        success: false,
        error: { message: 'Failed to create item', code: 'CREATE_ERROR' }
      }
    }
  }

  /**
   * Updates an existing item in the repository.
   * 
   * @param id - The unique identifier of the item to update
   * @param data - The partial data to update
   * @returns Promise resolving to the updated item
   * 
   * @example
   * ```typescript
   * const result = await repository.update('123', { name: 'Jane Doe' })
   * if (result.success) {
   *   console.log('Updated item:', result.data)
   * }
   * ```
   */
  async update(id: string, data: TUpdate): Promise<ApiResponse<T>> {
    try {
      // Execute update through circuit breaker
      const result = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .update(data as Record<string, unknown>)
          .eq('id', id)
          .select()
          .single()
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Database write operation failed', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { data: dbResult, error } = result.data!;

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true,
        data: dbResult as T
      }
    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error('Failed to update item'),
        {
          operation: 'repository-update',
          metadata: {
            tableName: this.tableName,
            id,
            dataKeys: Object.keys(data || {})
          }
        },
        'high'
      );
      return {
        success: false,
        error: { message: 'Failed to update item', code: 'UPDATE_ERROR' }
      }
    }
  }

  /**
   * Deletes an item from the repository.
   * 
   * @param id - The unique identifier of the item to delete
   * @returns Promise resolving to success status
   * 
   * @example
   * ```typescript
   * const result = await repository.delete('123')
   * if (result.success) {
   *   console.log('Item deleted successfully')
   * }
   * ```
   */
  async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Execute delete through circuit breaker
      const result = await this.writeCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .delete()
          .eq('id', id)
      });

      if (!result.success) {
        return {
          success: false,
          error: { message: result.error || 'Database delete operation failed', code: 'CIRCUIT_BREAKER_ERROR' }
        }
      }

      const { error } = result.data!;

      if (error) {
        return {
          success: false,
          error: { message: error.message, code: error.code }
        }
      }

      return {
        success: true
      }
    } catch (error) {
      ErrorLogger.log(
        error instanceof Error ? error : new Error('Failed to delete item'),
        {
          operation: 'repository-delete',
          metadata: {
            tableName: this.tableName,
            id
          }
        },
        'high'
      );
      return {
        success: false,
        error: { message: 'Failed to delete item', code: 'DELETE_ERROR' }
      }
    }
  }

  /**
   * Checks if an item exists in the repository.
   *
   * @param id - The unique identifier to check for existence
   * @returns Promise resolving to true if the item exists, false otherwise
   *
   * @example
   * ```typescript
   * const exists = await repository.exists('123')
   * if (exists) {
   *   console.log('Item exists')
   * }
   * ```
   */
  async exists(id: string): Promise<boolean> {
    try {
      // Execute exists check through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        return await this.db
          .from(this.tableName)
          .select('id', { count: 'exact', head: true })
          .eq('id', id)
      });

      if (!result.success) {
        const errorMessage = `Circuit breaker failed for exists check: ${result.error}`;
        ErrorLogger.log(
          new Error(errorMessage),
          {
            operation: 'repository-exists-circuit-breaker',
            metadata: { tableName: this.tableName, id }
          },
          'low'
        );
        return false;
      }

      const { count, error } = result.data!;

      if (error) {
        ErrorLogger.log(
          new Error(`Database error in exists check: ${error.message}`),
          {
            operation: 'repository-exists-database',
            metadata: { tableName: this.tableName, id, error: error.code }
          },
          'low'
        );
        return false;
      }

      return (count || 0) > 0;

    } catch (error) {
      const errorMessage = 'Failed to check item existence';
      ErrorLogger.log(
        error instanceof Error ? error : new Error(errorMessage),
        {
          operation: 'repository-exists',
          metadata: { tableName: this.tableName, id }
        },
        'low'
      );
      return false;
    }
  }

  /**
   * Returns the total count of items in the repository.
   *
   * @param filters - Optional filters to apply when counting
   * @returns Promise resolving to the total count of items matching the filters
   *
   * @example
   * ```typescript
   * const count = await repository.count()
   * console.log('Total count:', count)
   * ```
   */
  async count(filters?: Record<string, unknown>): Promise<number> {
    try {
      // Execute count through circuit breaker
      const result = await this.readCircuitBreaker.execute(async () => {
        let query = this.db.from(this.tableName).select('*', { count: 'exact', head: true })

        if (filters) {
          query = this.applyFilters(query, filters)
        }

        return await query
      });

      if (!result.success) {
        const errorMessage = `Circuit breaker failed for count: ${result.error}`;
        ErrorLogger.log(
          new Error(errorMessage),
          {
            operation: 'repository-count-circuit-breaker',
            metadata: { tableName: this.tableName, filters }
          },
          'low'
        );
        return 0;
      }

      const { count, error } = result.data!;

      if (error) {
        ErrorLogger.log(
          new Error(`Database error in count: ${error.message}`),
          {
            operation: 'repository-count-database',
            metadata: { tableName: this.tableName, filters, error: error.code }
          },
          'low'
        );
        return 0;
      }
      return count || 0;
    } catch (error) {
      const errorMessage = 'Failed to count items';
       ErrorLogger.log(
        error instanceof Error ? error : new Error(errorMessage),
        {
          operation: 'repository-count',
          metadata: { tableName: this.tableName, filters }
        },
        'low'
      );
      return 0;
    }
  }

  /**
   * Apply filters to a database query. Must be implemented by concrete repositories.
   * 
   * This method allows each repository to define its own filtering logic based on
   * the specific fields and requirements of the entity it manages.
   * 
   * @param query - The Supabase query builder instance
   * @param filters - Key-value pairs of filters to apply
   * @returns The modified query with filters applied
   * 
   * @example
   * ```typescript
   * protected applyFilters(query: any, filters: Record<string, any>) {
   *   if (filters.status) query = query.eq('status', filters.status)
   *   if (filters.createdAfter) query = query.gte('created_at', filters.createdAfter)
   *   return query
   * }
   * ```
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  protected abstract applyFilters(query: any, filters: Record<string, unknown>): any
}