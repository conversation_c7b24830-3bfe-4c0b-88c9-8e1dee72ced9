/**
 * Vocabulary Builder types and interfaces.
 * This file contains types specific to the vocabulary builder feature including
 * AWL words, word definitions, quiz templates, and related functionality.
 */

import type { 
  DbAwlWord, 
  DbWordDefinition, 
  DbWordBuildingNote, 
  DbVocabularyQuizTemplate 
} from './database'

/**
 * AWL (Academic Word List) word with all related information
 */
export interface AwlWord extends DbAwlWord {
  /** All definitions for different word forms */
  definitions?: WordDefinition[]
  /** Word building notes and etymology */
  buildingNotes?: WordBuildingNote[]
  /** Quiz templates for this word */
  quizTemplates?: VocabularyQuizTemplate[]
}

/**
 * Word definition with pronunciation and examples
 */
export interface WordDefinition extends DbWordDefinition {
  /** Parsed example sentences as array */
  examples?: string[]
  /** Parsed etymology information */
  etymologyDetails?: Etymology
}

/**
 * Word building notes for learning
 */
export interface WordBuildingNote extends Omit<DbWordBuildingNote, 'examples'> {
  /** Parsed examples as array */
  examples?: string[]
}

/**
 * Vocabulary quiz template
 */
export interface VocabularyQuizTemplate extends DbVocabularyQuizTemplate {
  /** Associated AWL word */
  awlWord?: AwlWord
}

/**
 * Etymology information
 */
export interface Etymology {
  /** Root word origin */
  root?: string
  /** Meaning of the root */
  meaning?: string
  /** Language of origin */
  language?: string
  /** Additional notes */
  notes?: string
}

/**
 * Word forms organized by part of speech
 */
export interface WordForms {
  /** Verb form */
  verb?: string
  /** Noun form */
  noun?: string
  /** Adjective form */
  adjective?: string
  /** Adverb form */
  adverb?: string
  /** Additional forms */
  [key: string]: string | undefined
}

/**
 * Text analysis result for AWL highlighting
 */
export interface AwlAnalysisResult {
  /** Original text */
  originalText: string
  /** Processed text with word positions */
  processedText: ProcessedTextSegment[]
  /** AWL words found in the text */
  awlWordsFound: AwlWordMatch[]
  /** Analysis metadata */
  metadata: AnalysisMetadata
}

/**
 * Text segment with highlighting information
 */
export interface ProcessedTextSegment {
  /** Text content */
  text: string
  /** Whether this segment is an AWL word */
  isAwlWord: boolean
  /** AWL word ID if applicable */
  awlWordId?: string
  /** Start position in original text */
  startIndex: number
  /** End position in original text */
  endIndex: number
}

/**
 * AWL word match in text
 */
export interface AwlWordMatch {
  /** AWL word information */
  word: AwlWord
  /** Positions where this word appears */
  positions: TextPosition[]
  /** Number of occurrences */
  count: number
}

/**
 * Position information for word in text
 */
export interface TextPosition {
  /** Start character index */
  start: number
  /** End character index */
  end: number
  /** Matched text (may include inflections) */
  matchedText: string
}

/**
 * Analysis metadata
 */
export interface AnalysisMetadata {
  /** Total word count */
  totalWords: number
  /** AWL word count */
  awlWordCount: number
  /** AWL coverage percentage */
  awlCoverage: number
  /** Processing time in milliseconds */
  processingTime: number
  /** AWL sublists represented */
  sublistsRepresented: number[]
}

/**
 * Complete word details for Word Master
 */
export interface WordDetails {
  /** Base AWL word information */
  word: AwlWord
  /** All definitions grouped by word form */
  definitionsByForm: Record<string, WordDefinition[]>
  /** Word building notes */
  buildingNotes: WordBuildingNote[]
  /** Generated quiz questions */
  quiz: WordQuiz
  /** Additional metadata */
  metadata: WordDetailsMetadata
}

/**
 * Word quiz for interactive learning
 */
export interface WordQuiz {
  /** Word being quizzed */
  wordId: string
  /** Quiz questions */
  questions: WordQuizQuestion[]
  /** Total questions */
  totalQuestions: number
  /** Estimated completion time */
  estimatedTime: number
}

/**
 * Individual quiz question
 */
export interface WordQuizQuestion {
  /** Question ID */
  id: string
  /** Question template with blank */
  questionText: string
  /** Correct answer */
  correctAnswer: string
  /** Word form being tested */
  wordForm: string
  /** Difficulty level */
  difficulty: number
  /** Hint for the user */
  hint?: string
  /** Additional context */
  context?: string
}

/**
 * User's quiz attempt
 */
export interface WordQuizAttempt {
  /** Quiz ID */
  quizId: string
  /** Word ID */
  wordId: string
  /** User's answers */
  answers: WordQuizAnswer[]
  /** Score achieved */
  score: number
  /** Total questions */
  totalQuestions: number
  /** Time taken in seconds */
  timeTaken: number
  /** Completion status */
  isCompleted: boolean
  /** Started timestamp */
  startedAt: string
  /** Completed timestamp */
  completedAt?: string
}

/**
 * User's answer to quiz question
 */
export interface WordQuizAnswer {
  /** Question ID */
  questionId: string
  /** User's answer */
  userAnswer: string
  /** Correct answer */
  correctAnswer: string
  /** Whether answer was correct */
  isCorrect: boolean
  /** Time taken for this question */
  timeTaken: number
  /** Whether user used a hint */
  usedHint: boolean
}

/**
 * Word details metadata
 */
export interface WordDetailsMetadata {
  /** Last updated timestamp */
  lastUpdated: string
  /** Data source version */
  dataVersion: string
  /** Completeness score */
  completeness: number
}

/**
 * Search request for Word Master
 */
export interface WordSearchRequest {
  /** Word to search for */
  query: string
  /** Include word forms in search */
  includeWordForms?: boolean
  /** Search mode */
  mode?: 'exact' | 'fuzzy' | 'contains'
}

/**
 * Search result from Word Master
 */
export interface WordSearchResult {
  /** Found word details */
  word?: WordDetails
  /** Whether word was found */
  found: boolean
  /** Search suggestions if not found */
  suggestions?: string[]
  /** Error message if any */
  error?: string
}

/**
 * Vocabulary learning progress
 */
export interface VocabularyProgress {
  /** User ID */
  userId: string
  /** Words studied */
  wordsStudied: string[]
  /** Quiz attempts */
  quizAttempts: WordQuizAttempt[]
  /** Current streak */
  currentStreak: number
  /** Total vocabulary score */
  totalScore: number
  /** Progress by AWL sublist */
  sublistProgress: Record<number, SublistProgress>
}

/**
 * Progress for specific AWL sublist
 */
export interface SublistProgress {
  /** Sublist number */
  sublist: number
  /** Total words in sublist */
  totalWords: number
  /** Words studied */
  wordsStudied: number
  /** Average quiz score */
  averageScore: number
  /** Completion percentage */
  completionPercentage: number
}

/**
 * Vocabulary service error types
 */
export type VocabularyError = 
  | 'WORD_NOT_FOUND'
  | 'INVALID_QUERY'
  | 'ANALYSIS_FAILED'
  | 'QUIZ_GENERATION_FAILED'
  | 'DATABASE_ERROR'
  | 'NETWORK_ERROR'

/**
 * Service response wrapper for vocabulary operations
 */
export interface VocabularyServiceResponse<T> {
  /** Response data */
  data: T | null
  /** Error information */
  error: VocabularyError | null
  /** Success status */
  success: boolean
  /** Additional message */
  message?: string
}

// =============================================================================
// USER PROGRESS TRACKING TYPES
// =============================================================================

/**
 * User's progress for a specific AWL word
 */
export interface VocabularyUserProgress {
  /** Progress ID */
  id: string
  /** User ID */
  userId: string
  /** AWL word ID */
  awlWordId: string
  /** Learning status */
  studyStatus: 'not_started' | 'learning' | 'mastered' | 'needs_review'
  /** Number of times studied */
  timesStudied: number
  /** Number of correct answers */
  timesCorrect: number
  /** Number of incorrect answers */
  timesIncorrect: number
  /** Accuracy percentage */
  accuracyPercentage: number
  /** Best quiz score */
  bestQuizScore: number
  /** Average quiz score */
  averageQuizScore: number
  /** Total quiz attempts */
  totalQuizAttempts: number
  /** Total study time in seconds */
  totalStudyTimeSeconds: number
  /** Last studied timestamp */
  lastStudiedAt?: string
  /** Mastered timestamp */
  masteredAt?: string
  /** Whether word is bookmarked */
  isBookmarked: boolean
  /** User's difficulty rating */
  difficultyRating?: number
  /** Created timestamp */
  createdAt: string
  /** Updated timestamp */
  updatedAt: string
  /** Associated AWL word */
  awlWord?: AwlWord
}

/**
 * Study session tracking
 */
export interface VocabularyStudySession {
  /** Session ID */
  id: string
  /** User ID */
  userId: string
  /** Type of study session */
  sessionType: 'awl_analysis' | 'word_master' | 'quiz' | 'review'
  /** Session start time */
  startedAt: string
  /** Session end time */
  endedAt?: string
  /** Session duration in seconds */
  durationSeconds?: number
  /** Words studied in this session */
  wordsStudied: string[]
  /** New AWL words discovered */
  awlWordsDiscovered: string[]
  /** Text analyzed (for AWL highlighter) */
  textAnalyzed?: string
  /** Questions answered */
  questionsAnswered: number
  /** Questions correct */
  questionsCorrect: number
  /** Accuracy percentage */
  accuracyPercentage: number
  /** Tools used in session */
  toolsUsed: string[]
  /** Number of actions taken */
  actionsTaken: number
  /** Created timestamp */
  createdAt: string
}

/**
 * Vocabulary-specific badge
 */
export interface VocabularyBadge {
  /** Badge ID */
  id: string
  /** Unique badge key */
  badgeKey: string
  /** Badge name */
  name: string
  /** Badge description */
  description: string
  /** Icon component name */
  iconName: string
  /** Badge color theme */
  color: string
  /** Badge category */
  badgeCategory: 'milestone' | 'streak' | 'accuracy' | 'exploration' | 'mastery' | 'engagement'
  /** Requirement type */
  requirementType: 'count' | 'percentage' | 'streak' | 'score' | 'time'
  /** Requirement value */
  requirementValue: number
  /** Human-readable requirement */
  requirementDescription: string
  /** Badge tier */
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  /** Points awarded */
  pointsAwarded: number
  /** Whether badge can be earned multiple times */
  isRepeatable: boolean
  /** Whether badge is hidden until earned */
  isHidden: boolean
  /** Sort order */
  sortOrder: number
  /** Whether badge is active */
  isActive: boolean
  /** Created timestamp */
  createdAt: string
}

/**
 * User's earned vocabulary badge
 */
export interface UserVocabularyBadge {
  /** Record ID */
  id: string
  /** User ID */
  userId: string
  /** Badge ID */
  badgeId: string
  /** When badge was earned */
  earnedAt: string
  /** User progress snapshot when earned */
  progressWhenEarned: Record<string, any>
  /** Session where badge was earned */
  sessionId?: string
  /** Instance number for repeatable badges */
  instanceNumber: number
  /** Badge details */
  badge?: VocabularyBadge
}

/**
 * Learning streak tracking
 */
export interface VocabularyStreak {
  /** Streak ID */
  id: string
  /** User ID */
  userId: string
  /** Type of streak */
  streakType: 'daily_study' | 'quiz_perfect' | 'word_mastery'
  /** Current streak count */
  currentCount: number
  /** Best streak count */
  bestCount: number
  /** Last activity date */
  lastActivityDate: string
  /** Streak start date */
  streakStartDate: string
  /** Activity history */
  activities: any[]
  /** Created timestamp */
  createdAt: string
  /** Updated timestamp */
  updatedAt: string
}

/**
 * Comprehensive user vocabulary dashboard data
 */
export interface VocabularyDashboard {
  /** User's overall statistics */
  stats: VocabularyStats
  /** Progress by AWL sublist */
  sublistProgress: SublistProgress[]
  /** Recently studied words */
  recentWords: VocabularyUserProgress[]
  /** Earned badges */
  badges: UserVocabularyBadge[]
  /** Available badges to earn */
  availableBadges: VocabularyBadge[]
  /** Current streaks */
  streaks: VocabularyStreak[]
  /** Recent study sessions */
  recentSessions: VocabularyStudySession[]
  /** Recommended actions */
  recommendations: VocabularyRecommendation[]
}

/**
 * User's vocabulary statistics
 */
export interface VocabularyStats {
  /** Total words studied */
  totalWordsStudied: number
  /** Words mastered */
  wordsMastered: number
  /** Current study streak */
  currentStreak: number
  /** Best study streak */
  bestStreak: number
  /** Total study time in seconds */
  totalStudyTime: number
  /** Average quiz score */
  averageQuizScore: number
  /** Total quiz attempts */
  totalQuizAttempts: number
  /** Total points earned */
  totalPoints: number
  /** Current level/rank */
  currentLevel: number
  /** AWL coverage percentage */
  awlCoverage: number
  /** Favorite study time */
  favoriteStudyTime?: string
  /** Most productive day */
  mostProductiveDay?: string
}

/**
 * Recommendation for user
 */
export interface VocabularyRecommendation {
  /** Recommendation type */
  type: 'study_word' | 'review_word' | 'take_quiz' | 'analyze_text' | 'earn_badge'
  /** Recommendation title */
  title: string
  /** Recommendation description */
  description: string
  /** Action to take */
  action: string
  /** Priority level */
  priority: 'low' | 'medium' | 'high'
  /** Associated data */
  data?: any
}

/**
 * Badge progress tracking
 */
export interface BadgeProgress {
  /** Badge being tracked */
  badge: VocabularyBadge
  /** Current progress value */
  currentProgress: number
  /** Required value */
  requiredValue: number
  /** Progress percentage */
  progressPercentage: number
  /** Whether badge is earned */
  isEarned: boolean
  /** Estimated time to earn */
  estimatedTimeToEarn?: string
}