/**
 * Unified Learning Dashboard Types
 * Combines quiz progress and vocabulary progress into a single dashboard view
 */

// Base types for unified dashboard
export interface UnifiedDashboardData {
  overallStats: OverallLearningStats;
  weeklyData: WeeklyActivity[];
  sublistProgress: AWLSublistProgress[];
  recentActivity: CombinedLearningActivity[];
  achievements: UnifiedBadge[];
  weeklyGoal: number;
  weeklyProgress: number;
}

// Combined statistics from both quiz and vocabulary systems
export interface OverallLearningStats {
  // Quiz-related stats
  totalQuizAttempts: number;
  averageQuizScore: number;
  bestQuizScore: number;
  totalQuizTimeSpent: number;
  
  // Vocabulary-related stats
  totalWordsStudied: number;
  wordsMastered: number;
  currentStreak: number;
  bestStreak: number;
  awlCoverage: number;
  
  // Combined metrics
  totalStudyTime: number;
  totalPoints: number;
  currentLevel: number;
}

// Weekly activity data combining all learning types
export interface WeeklyActivity {
  day: string;
  date: string;
  quizzes: number;
  wordsStudied: number;
  totalTimeSpent: number;
  points: number;
}

// AWL Sublist progress tracking
export interface AWLSublistProgress {
  sublist: number;
  totalWords: number;
  wordsStudied: number;
  wordsMastered: number;
  completionPercentage: number;
  averageScore: number;
  lastActivity?: string;
}

// Combined learning activity for timeline
export interface CombinedLearningActivity {
  id: string;
  type: 'quiz' | 'vocabulary' | 'word_study';
  timestamp: string;
  title: string;
  description?: string;
  
  // Quiz-specific data
  score?: number;
  correctAnswers?: number;
  totalQuestions?: number;
  mode?: 'practice' | 'test';
  
  // Vocabulary-specific data
  wordsLearned?: number;
  wordsMastered?: number;
  sublist?: number;
  
  // Common data
  timeSpent: number;
  points: number;
  details?: Record<string, any>;
}

// Unified badge/achievement system
export interface UnifiedBadge {
  id: string;
  name: string;
  description: string;
  iconUrl?: string;
  category: 'quiz' | 'vocabulary' | 'streak' | 'milestone' | 'time';
  earnedAt: string;
  criteria: {
    type: string;
    threshold: number;
    description: string;
  };
  isActive: boolean;
}

// Dashboard section configurations
export interface DashboardSection {
  id: string;
  title: string;
  component: string;
  priority: number;
  visible: boolean;
  config?: Record<string, any>;
}

// Learning tool quick access
export interface LearningTool {
  id: string;
  name: string;
  description: string;
  icon: string;
  route: string;
  category: 'vocabulary' | 'quiz' | 'assessment';
  featured: boolean;
  usageStats?: {
    lastUsed?: string;
    timesUsed: number;
    averageSessionTime: number;
  };
}

// Dashboard preferences and customization
export interface DashboardPreferences {
  userId: string;
  layout: 'default' | 'compact' | 'detailed';
  visibleSections: string[];
  weeklyGoal: number;
  timeZone: string;
  notifications: {
    dailyReminder: boolean;
    weeklyReport: boolean;
    achievements: boolean;
  };
}

// API response types
export interface DashboardResponse {
  data: UnifiedDashboardData;
  lastUpdated: string;
  cacheExpiry: number;
}

export interface DashboardError {
  code: string;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
}

// Loading states for different dashboard sections
export interface DashboardLoadingState {
  overall: boolean;
  stats: boolean;
  weekly: boolean;
  sublists: boolean;
  activity: boolean;
  achievements: boolean;
}

// Export utility types
export type DashboardSectionKey = keyof DashboardLoadingState;
export type LearningActivityType = CombinedLearningActivity['type'];
export type BadgeCategory = UnifiedBadge['category'];