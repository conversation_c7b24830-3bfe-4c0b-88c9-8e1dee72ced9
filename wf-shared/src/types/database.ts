/**
 * Core database types generated from Supabase.
 * This file contains ONLY database schema types - no admin-specific logic.
 * 
 * These types provide type safety for database operations and ensure
 * consistency between the database schema and TypeScript code.
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      badges: {
        Row: {
          badge_type: string
          created_at: string | null
          criteria: Json
          description: string
          icon_url: string | null
          id: string
          is_active: boolean | null
          name: string
        }
        Insert: {
          badge_type: string
          created_at?: string | null
          criteria: Json
          description: string
          icon_url?: string | null
          id?: string
          is_active?: boolean | null
          name: string
        }
        Update: {
          badge_type?: string
          created_at?: string | null
          criteria?: Json
          description?: string
          icon_url?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
        }
        Relationships: []
      }
      categories: {
        Row: {
          category_type: string | null
          color: string | null
          created_at: string | null
          description: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          key: string
          metadata: Json | null
          name: string | null
          parent_id: string | null
          sort_order: number | null
        }
        Insert: {
          category_type?: string | null
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          key: string
          metadata?: Json | null
          name?: string | null
          parent_id?: string | null
          sort_order?: number | null
        }
        Update: {
          category_type?: string | null
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          key?: string
          metadata?: Json | null
          name?: string | null
          parent_id?: string | null
          sort_order?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      explanations: {
        Row: {
          content: string
          created_at: string | null
          explanation_type: string
          id: string
          metadata: Json | null
          question_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          explanation_type?: string
          id?: string
          metadata?: Json | null
          question_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          explanation_type?: string
          id?: string
          metadata?: Json | null
          question_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "explanations_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
        ]
      }
      levels: {
        Row: {
          description: string | null
          id: string
          is_active: boolean | null
          key: string
          name: string
          sort_order: number | null
          system: string
        }
        Insert: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          key: string
          name: string
          sort_order?: number | null
          system: string
        }
        Update: {
          description?: string | null
          id?: string
          is_active?: boolean | null
          key?: string
          name?: string
          sort_order?: number | null
          system?: string
        }
        Relationships: []
      }
      question_options: {
        Row: {
          created_at: string | null
          id: string
          is_correct: boolean
          option_key: string
          option_text: string
          question_id: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_correct?: boolean
          option_key: string
          option_text: string
          question_id: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_correct?: boolean
          option_key?: string
          option_text?: string
          question_id?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "question_options_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
        ]
      }
      question_types: {
        Row: {
          config_schema: Json | null
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          key: string
          max_options: number | null
          min_options: number | null
          name: string
          requires_options: boolean | null
        }
        Insert: {
          config_schema?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          key: string
          max_options?: number | null
          min_options?: number | null
          name: string
          requires_options?: boolean | null
        }
        Update: {
          config_schema?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          key?: string
          max_options?: number | null
          min_options?: number | null
          name?: string
          requires_options?: boolean | null
        }
        Relationships: []
      }
      questions: {
        Row: {
          created_at: string | null
          difficulty_level: number
          id: string
          is_active: boolean | null
          metadata: Json | null
          question_text: string
          question_type_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          difficulty_level?: number
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          question_text: string
          question_type_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          difficulty_level?: number
          id?: string
          is_active?: boolean | null
          metadata?: Json | null
          question_text?: string
          question_type_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "questions_question_type_id_fkey"
            columns: ["question_type_id"]
            isOneToOne: false
            referencedRelation: "question_types"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_questions: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean | null
          order_index: number
          points: number | null
          question_id: string
          quiz_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          order_index: number
          points?: number | null
          question_id: string
          quiz_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          order_index?: number
          points?: number | null
          question_id?: string
          quiz_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quiz_questions_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quiz_questions_quiz_id_fkey"
            columns: ["quiz_id"]
            isOneToOne: false
            referencedRelation: "quizzes"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_attempts: {
        Row: {
          completed_at: string | null
          correct_answers: number
          created_at: string | null
          id: string
          is_completed: boolean | null
          mode: string
          quiz_id: string | null
          score: number
          time_taken_seconds: number | null
          total_questions: number
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          completed_at?: string | null
          correct_answers?: number
          created_at?: string | null
          id?: string
          is_completed?: boolean | null
          mode: string
          quiz_id?: string | null
          score?: number
          time_taken_seconds?: number | null
          total_questions: number
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          completed_at?: string | null
          correct_answers?: number
          created_at?: string | null
          id?: string
          is_completed?: boolean | null
          mode?: string
          quiz_id?: string | null
          score?: number
          time_taken_seconds?: number | null
          total_questions?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quiz_attempts_quiz_id_fkey"
            columns: ["quiz_id"]
            isOneToOne: false
            referencedRelation: "quizzes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quiz_attempts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_types: {
        Row: {
          config_schema: Json | null
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          key: string
          name: string
        }
        Insert: {
          config_schema?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          key: string
          name: string
        }
        Update: {
          config_schema?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          key?: string
          name?: string
        }
        Relationships: []
      }
      quizzes: {
        Row: {
          category_id: string
          created_at: string | null
          description: string | null
          difficulty_level: number
          id: string
          instructions: string | null
          is_active: boolean | null
          key: string
          level_id: string
          quiz_config: Json | null
          quiz_type_id: string | null
          time_limit_minutes: number | null
          title: string | null
          total_questions: number
          updated_at: string | null
        }
        Insert: {
          category_id: string
          created_at?: string | null
          description?: string | null
          difficulty_level?: number
          id?: string
          instructions?: string | null
          is_active?: boolean | null
          key: string
          level_id: string
          quiz_config?: Json | null
          quiz_type_id?: string | null
          time_limit_minutes?: number | null
          title?: string | null
          total_questions?: number
          updated_at?: string | null
        }
        Update: {
          category_id?: string
          created_at?: string | null
          description?: string | null
          difficulty_level?: number
          id?: string
          instructions?: string | null
          is_active?: boolean | null
          key?: string
          level_id?: string
          quiz_config?: Json | null
          quiz_type_id?: string | null
          time_limit_minutes?: number | null
          title?: string | null
          total_questions?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quizzes_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quizzes_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quizzes_quiz_type_id_fkey"
            columns: ["quiz_type_id"]
            isOneToOne: false
            referencedRelation: "quiz_types"
            referencedColumns: ["id"]
          },
        ]
      }
      user_answers: {
        Row: {
          attempt_id: string | null
          created_at: string | null
          id: string
          is_correct: boolean
          question_id: string | null
          time_taken_seconds: number | null
          user_answer: string
        }
        Insert: {
          attempt_id?: string | null
          created_at?: string | null
          id?: string
          is_correct: boolean
          question_id?: string | null
          time_taken_seconds?: number | null
          user_answer: string
        }
        Update: {
          attempt_id?: string | null
          created_at?: string | null
          id?: string
          is_correct?: boolean
          question_id?: string | null
          time_taken_seconds?: number | null
          user_answer?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_answers_attempt_id_fkey"
            columns: ["attempt_id"]
            isOneToOne: false
            referencedRelation: "quiz_attempts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_answers_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
        ]
      }
      user_badges: {
        Row: {
          badge_id: string | null
          earned_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          badge_id?: string | null
          earned_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          badge_id?: string | null
          earned_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_badges_badge_id_fkey"
            columns: ["badge_id"]
            isOneToOne: false
            referencedRelation: "badges"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_badges_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_progress: {
        Row: {
          average_score: number | null
          best_score: number | null
          category_id: string
          created_at: string | null
          current_streak: number | null
          id: string
          last_attempt_at: string | null
          level_id: string
          longest_streak: number | null
          total_attempts: number | null
          total_correct: number | null
          total_questions: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          average_score?: number | null
          best_score?: number | null
          category_id: string
          created_at?: string | null
          current_streak?: number | null
          id?: string
          last_attempt_at?: string | null
          level_id: string
          longest_streak?: number | null
          total_attempts?: number | null
          total_correct?: number | null
          total_questions?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          average_score?: number | null
          best_score?: number | null
          category_id?: string
          created_at?: string | null
          current_streak?: number | null
          id?: string
          last_attempt_at?: string | null
          level_id?: string
          longest_streak?: number | null
          total_attempts?: number | null
          total_correct?: number | null
          total_questions?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_progress_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_progress_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_progress_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string | null
          email: string
          first_name: string
          id: string
          last_name: string
          level_id: string
          role: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          first_name: string
          id: string
          last_name: string
          level_id: string
          role?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          first_name?: string
          id?: string
          last_name?: string
          level_id?: string
          role?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels"
            referencedColumns: ["id"]
          },
        ]
      }
      awl_words: {
        Row: {
          created_at: string | null
          frequency_rank: number | null
          id: string
          is_active: boolean | null
          sublists: number
          updated_at: string | null
          word: string
          word_forms: Json | null
        }
        Insert: {
          created_at?: string | null
          frequency_rank?: number | null
          id?: string
          is_active?: boolean | null
          sublists: number
          updated_at?: string | null
          word: string
          word_forms?: Json | null
        }
        Update: {
          created_at?: string | null
          frequency_rank?: number | null
          id?: string
          is_active?: boolean | null
          sublists?: number
          updated_at?: string | null
          word?: string
          word_forms?: Json | null
        }
        Relationships: []
      }
      word_definitions: {
        Row: {
          awl_word_id: string | null
          created_at: string | null
          english_definition: string | null
          etymology: Json | null
          example_sentences: Json | null
          id: string
          ipa_pronunciation: string | null
          vietnamese_meaning: string | null
          word_form: string
          word_text: string
        }
        Insert: {
          awl_word_id?: string | null
          created_at?: string | null
          english_definition?: string | null
          etymology?: Json | null
          example_sentences?: Json | null
          id?: string
          ipa_pronunciation?: string | null
          vietnamese_meaning?: string | null
          word_form: string
          word_text: string
        }
        Update: {
          awl_word_id?: string | null
          created_at?: string | null
          english_definition?: string | null
          etymology?: Json | null
          example_sentences?: Json | null
          id?: string
          ipa_pronunciation?: string | null
          vietnamese_meaning?: string | null
          word_form?: string
          word_text?: string
        }
        Relationships: [
          {
            foreignKeyName: "word_definitions_awl_word_id_fkey"
            columns: ["awl_word_id"]
            isOneToOne: false
            referencedRelation: "awl_words"
            referencedColumns: ["id"]
          },
        ]
      }
      word_building_notes: {
        Row: {
          awl_word_id: string | null
          content: string
          created_at: string | null
          examples: Json | null
          id: string
          note_type: string
        }
        Insert: {
          awl_word_id?: string | null
          content: string
          created_at?: string | null
          examples?: Json | null
          id?: string
          note_type: string
        }
        Update: {
          awl_word_id?: string | null
          content?: string
          created_at?: string | null
          examples?: Json | null
          id?: string
          note_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "word_building_notes_awl_word_id_fkey"
            columns: ["awl_word_id"]
            isOneToOne: false
            referencedRelation: "awl_words"
            referencedColumns: ["id"]
          },
        ]
      }
      vocabulary_quiz_templates: {
        Row: {
          answer_form: string
          awl_word_id: string | null
          created_at: string | null
          difficulty_level: number | null
          id: string
          question_template: string
          word_form: string
        }
        Insert: {
          answer_form: string
          awl_word_id?: string | null
          created_at?: string | null
          difficulty_level?: number | null
          id?: string
          question_template: string
          word_form: string
        }
        Update: {
          answer_form?: string
          awl_word_id?: string | null
          created_at?: string | null
          difficulty_level?: number | null
          id?: string
          question_template?: string
          word_form?: string
        }
        Relationships: [
          {
            foreignKeyName: "vocabulary_quiz_templates_awl_word_id_fkey"
            columns: ["awl_word_id"]
            isOneToOne: false
            referencedRelation: "awl_words"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Helper types for better developer experience
type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

// Constants for database schema
export const Constants = {
  public: {
    Enums: {},
  },
} as const

/**
 * Common database row types for easy access.
 * These type aliases provide convenient access to database table row types.
 */

/** User table row type */
export type DbUser = Database['public']['Tables']['users']['Row']
/** Level table row type */
export type DbLevel = Database['public']['Tables']['levels']['Row']
/** Category table row type */
export type DbCategory = Database['public']['Tables']['categories']['Row']
/** Quiz table row type */
export type DbQuiz = Database['public']['Tables']['quizzes']['Row']
/** Question table row type */
export type DbQuestion = Database['public']['Tables']['questions']['Row']
/** Question option table row type */
export type DbQuestionOption = Database['public']['Tables']['question_options']['Row']
/** Question type table row type */
export type DbQuestionType = Database['public']['Tables']['question_types']['Row']
/** Quiz type table row type */
export type DbQuizType = Database['public']['Tables']['quiz_types']['Row']
/** Quiz attempt table row type */
export type DbQuizAttempt = Database['public']['Tables']['quiz_attempts']['Row']
/** User answer table row type */
export type DbUserAnswer = Database['public']['Tables']['user_answers']['Row']
/** User progress table row type */
export type DbUserProgress = Database['public']['Tables']['user_progress']['Row']
/** Explanation table row type */
export type DbExplanation = Database['public']['Tables']['explanations']['Row']
/** Badge table row type */
export type DbBadge = Database['public']['Tables']['badges']['Row']
/** User badge table row type */
export type DbUserBadge = Database['public']['Tables']['user_badges']['Row']
/** Quiz question junction table row type */
export type DbQuizQuestion = Database['public']['Tables']['quiz_questions']['Row']
/** AWL word table row type */
export type DbAwlWord = Database['public']['Tables']['awl_words']['Row']
/** Word definition table row type */
export type DbWordDefinition = Database['public']['Tables']['word_definitions']['Row']
/** Word building notes table row type */
export type DbWordBuildingNote = Database['public']['Tables']['word_building_notes']['Row']
/** Vocabulary quiz template table row type */
export type DbVocabularyQuizTemplate = Database['public']['Tables']['vocabulary_quiz_templates']['Row']

/**
 * User with admin-specific relations for admin portal.
 * Extends the base user type with additional relationships needed for admin functionality.
 * 
 * @example
 * ```typescript
 * const adminUser: UserWithAdminRelations = {
 *   id: 'user_123',
 *   email: '<EMAIL>',
 *   levels: { id: 'level_1', name: 'Advanced' },
 *   user_progress: [{ completion_rate: 85 }],
 *   quiz_attempts: [{ score: 92, completed_at: '2023-10-01' }]
 * }
 * ```
 */
export interface UserWithAdminRelations extends DbUser {
  /** Associated level information */
  levels?: DbLevel
  /** User's progress records */
  user_progress?: DbUserProgress[]
  /** User's quiz attempts */
  quiz_attempts?: DbQuizAttempt[]
}