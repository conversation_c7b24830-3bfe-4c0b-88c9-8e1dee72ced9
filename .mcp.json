{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref=pwjzxfdfmqsdukszufvn"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "serena": {"type": "stdio", "command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "/Users/<USER>/Working/VibeCoding/WordFormation"], "env": {}}}}