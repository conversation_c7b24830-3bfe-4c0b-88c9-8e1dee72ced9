{"permissions": {"allow": ["<PERSON><PERSON>(pkill:*)"], "deny": []}, "includeCoAuthoredBy": false, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"], "hooks": {"pre_code_change": ["echo '🤖 Claude: Reading coding standards...'", "head -20 /Users/<USER>/Working/VibeCoding/WordFormation/documentation/DEVELOPMENT_STANDARDS.md"], "post_code_change": ["echo '🔍 Claude: Validating code changes...'", "cd /Users/<USER>/Working/VibeCoding/WordFormation/wf-frontend && npx tsc --noEmit || echo '❌ Frontend TypeScript errors found'", "cd /Users/<USER>/Working/VibeCoding/WordFormation/wf-admin-portal && npx tsc --noEmit || echo '❌ Admin Portal TypeScript errors found'"]}, "reminders": ["🚨 ALWAYS check /documentation/DEVELOPMENT_STANDARDS.md before code changes", "📋 Use 3-file pattern: Component.tsx + Component.handler.ts + Component.style.ts", "🚫 No TypeScript errors allowed - run 'npx tsc --noEmit' first", "🔧 No direct API calls in .tsx files - use service layer", "📝 Follow import order: React → Libraries → UI → Services → Icons → Local"], "workflows": {"pre_development": ["Read(/documentation/DEVELOPMENT_STANDARDS.md)", "Bash(npx tsc --noEmit)", "Bash(npm run lint)"], "post_development": ["Bash(npx tsc --noEmit)", "Bash(npm run lint)", "Bash(npm test)"]}}